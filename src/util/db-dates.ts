import { Application } from '@feathersjs/feathers';
import { DynamoDBStreams } from 'aws-sdk/clients/all';
import { Knex } from 'knex';
import moment from 'moment';

// import * as DBT from "../../../types/db-types";

export const sec_ms = 1000;
export const min_ms = sec_ms*60;
export const hr_ms = min_ms*60;
export const day_ms = hr_ms*24;

// checking if a retrieved date is in the past
export const isDatePast = (knexDate:any, daysOffset:number=0) => {
    const d0 = new Date().valueOf()
    const d1 = new Date(<any>knexDate).valueOf()
    return (d0 + daysOffset*day_ms) > d1;
}

export const subtractDates = (knexDate:any, dateToSubtract:any) => {
    const d0 = new Date().valueOf();
    const d1 = new Date().valueOf();
    return new Date(d0 - d1);
}

export const dbDateNow = (app:Application) => {
    const db: Knex = app.get('knexClientWrite');
    return db.fn.now();
}
export const dbDateOffsetDays = (app:Application, days:number=2) => {
    const db: Knex = app.get('knexClientWrite');
    return db.raw('date_add(?, INTERVAL ? day)', [db.fn.now(), days]);
}
export const dbDateOffsetHours = (app:Application, hours:number=1) => {
    const db: Knex = app.get('knexClientWrite');
    return db.raw('date_add(?, INTERVAL ? hour)', [db.fn.now(), hours]);
}
export const dateAsUtcDbString = (date?:moment.MomentInput) => {
  return moment.utc(date).format('YYYY-MM-DD HH:mm:ss');
}
export const dbDateSetDateTime = (app:Application, hours:number, dateTime:string) => {
    const db: Knex = app.get('knexClientWrite');
    return db.raw('date_add(?, INTERVAL ? hour)', [dateTime, hours]);
}
