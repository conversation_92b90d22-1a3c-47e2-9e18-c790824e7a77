import { Application, Paginated } from "@feathersjs/feathers";
import { Redis } from "ioredis";
import { Errors } from "../errors/general";
import { RedisKeyPrefixes } from "../redis/redis";

export const getSysConstString = async (app:Application, FLAG_KEY:string) => {
  const redis: Redis = app.get('redis');
  const redisValue = await redis.hget(RedisKeyPrefixes.SysConstantsString, FLAG_KEY);
  if (redisValue) {
    return redisValue;
  }

  const records = <Paginated<any>> await app
    .service('db/read/sys-constants-string')
    .find({query: {key: FLAG_KEY} })

  if (!records.data[0]){
    throw new Errors.BadGateway('INVALID_CONSTANT');
  }
  const value = records.data[0].value;
  redis.hset(RedisKeyPrefixes.SysConstantsString, FLAG_KEY, value); // not awaiting
  return records.data[0].value;
}