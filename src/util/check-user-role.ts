import { Application } from "../declarations";
import { Errors } from "../errors/general";
import { dbRawReadReporting } from "./db-raw"

/**
 * check if user has test_ctrl_issue_revw_exempt_ovr u_role_type
 */
export const userHasRole = async (app: Application,role_type:string, uid: number) => {
    const IssueReviewroles = await dbRawReadReporting(app, { role_type, uid }, `
    
       select * from user_roles where is_revoked = 0 and role_type = :role_type and uid = :uid limit 1
    ;`)
    
    if(IssueReviewroles.length > 0){
      return 
    }

    throw new Errors.NotAuthenticated('MISSING_REQUIRED_USER_ROLE')
}