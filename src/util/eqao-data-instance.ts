import { Application } from '../declarations';
import { EC2 } from 'aws-sdk';
import { delay } from 'bluebird';
import Axios from 'axios';
import logger from '../logger';

export default class EqaoDataInstance {

  private dataInstanceParams:{InstanceIds:string[]};
  private ec2:EC2;
  private app: Application;

  constructor (app:Application) {
    this.app = app;

    if (app.get('isLocalDataServer')){
      this.dataInstanceParams = {InstanceIds: []}
      this.ec2 = <any> {};
    }
    else {
      this.dataInstanceParams = {
        InstanceIds: [ app.get('eqaoDataInstanceId') ]
      }
      this.ec2 = new EC2(app.get('awsCredentials'));
    }
  }

  async describeDataInstance () {
    const response = await this.ec2.describeInstances(this.dataInstanceParams).promise();
    try {
      return response.Reservations?.pop()?.Instances?.pop()
    } catch (e) {
      return null;
    }
  }


  async isDataApiUp (apiBase?:string) {
    if (!(await this.isInstanceStarted())) {
      return false;
    }
    apiBase = apiBase || await this.getApiBase();
    try {
      const healthResponse = await Axios.get(`${apiBase}/public/health`);
      const health = healthResponse.data;
      return health?.readReadable?.passed && health?.writeWritable?.passed;
    } catch (e) {
      logger.error('could not contact data instance api on health endpoint', e, { apiBase });
      return false;
    }
  }

  async isInstanceStarted () {
    try {
      const instanceDesc = await this.describeDataInstance();
      return instanceDesc?.State?.Name === 'running';
    } catch (e) {
      return false;
    }
  }

  async getApiBase () {
    if(this.app.get('isLocalDataServer')) {
      return 'http://localhost:3030';
    }
    const dataInstanceIP = await this.getInstanceIP();
    return `http://${dataInstanceIP}:3030`;
  }

  async getInstanceIP () {
    try {
      const instanceDesc = await this.describeDataInstance();
      return instanceDesc?.PrivateIpAddress
    } catch (e) {
      logger.error('could not get data instance ip', e);
      return false;
    }
  }

  async startDataInstance () {
    await this.ec2.startInstances(this.dataInstanceParams).promise();
    do {
      await delay(10000);
    } while (!(await this.isInstanceStarted()))
  }
}
