export const intersectSets = (setA:Set<any>, setB:Set<any>) => {
  // Convert setB to an array and use the filter method to find elements that are also in setA
  const intersection = new Set([...setB].filter(x => setA.has(x)));
  return intersection;
}

export const intersectArrs = (arrA:Array<any>, arrB:Array<any>) => {
  // Convert setB to an array and use the filter method to find elements that are also in setA
  const setA = new Set(arrA);
  return arrB.filter(x => setA.has(x));
}

export const differenceSets = (setA:Set<any>, setB:Set<any>) => {
  // Convert setA to an array and use the filter method to find elements that are not in setB
  const difference = new Set([...setA].filter(x => !setB.has(x)));
  return difference;
}

export const addToArrayIfNotPresent = <T>(arr: T[], element: T) => {
  if (!arr.includes(element)) {
    arr.push(element);
  }
  return arr;
}

export const uniqueProps = (arr:any[], prop:string) => {
  return [... new Set(arr.map(r => r[prop]))]
}