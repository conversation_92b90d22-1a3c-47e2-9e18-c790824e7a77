import { Application } from "../declarations";
import { Errors } from '../errors/general';


export enum TypeConvertSourceType {
    ASMT_SLUG = "ASMT_SLUG",
    ClassGroupType = "ClassGroupType",
    SessionTitle = 'SessionTitle',
    TestWindowType = "TestWindowType",
    AssessmentSlug = "AssessmentSlug",
}

export enum TypeConvertTargetType {
    ASMT_SLUG_Name = "ASMT_SLUG_Name",
    FrenchImmersionSDCKey = "FrenchImmersionSDCKey",
    FrenchImmersionSDCKeyNamespace = "FrenchImmersionSDCKeyNamespace",
    TermFormatSDCKey = " TermFormatSDCKey",
    TermFormatSDCKeyNamespace = " TermFormatSDCKeyNamespace",
    ScanSlug = "ScanSlug",
    TranslationSlug = "TranslationSlug",
    UserMetaKeyNamespace = "UserMetaKeyNamespace",
    AssessmentSlug = "AssessmentSlug",
    TestWindowType = "TestWindowType",
}

export const convertTypesContainer:any[] = [];

export const convertTypes = async (app:Application, source_type:string, target_type:string, source:string) => {
    if (!app || !source_type || !target_type || !source){
        throw new Errors.BadRequest();
    }
    let returnValue;

    //tried to find it in ConvertTypesContrainer
    const inConvertTypesContrainer =
        convertTypesContainer.find( (convertType:any) =>
            convertType.source_type == source_type
         && convertType.target_type == target_type
         && convertType.source == source)

    if(inConvertTypesContrainer){
        returnValue = inConvertTypesContrainer.target
    }else{
        // fetch it from database if can't find in ConvertTypesContrainer
        const records = await app.service('db/read/type-convert')
        .db()
        .where('source_type', source_type)
        .where('target_type', target_type)
        .where('source', source)
        .where('is_revoked', 0)
        .select('target');
        if (records && records.length > 0 && records[0]) {
            returnValue = records[0].target;

            //stored in the ConvertTypesContrainer to be used in future
            convertTypesContainer.push({
                source_type,
                target_type,
                source,
                target:records[0].target
            })
        };
    }

    return returnValue;;
}
