// source: https://bubo.vretta.com/ryanschwarz/eqao-psychometric-pipeline/-/blob/93a3814423302000d2cef3f0ed675fb451f313fd/preset-scripts/run_scalescore_osslt.R

import { IThetaRow, IScaleScore, IScaleValidationRow, IValidationError } from "./types";

export const assignScaleScores = (thetas: IThetaRow[], scaleScoreMappings: IScaleScore[], targetLang: string) : void /*IScaleValidationRow[]*/ => {
  
  // filter and sort scale score mappings for target language
  const sortedScalescore = scaleScoreMappings
    .filter((row) => row.lang === targetLang)
    .sort((a, b) => b.scale_score - a.scale_score ); // desc
    // min index is the highest score
  const scaleScoreCutoffs: number[] = sortedScalescore.map((row) => row.theta);


  // todo: store special scale score in scale score table
  // warning: this could cause a discrepancy for the more simplified approach that may be done to compare
  const overrideScaleScoreIndex = sortedScalescore.findIndex(ss => ss.scale_score == 300);
  const maxScaleScoreIndex = sortedScalescore.findIndex(ss => ss.scale_score == 400);

  // assign scale scores to thetas
  thetas.forEach((row) => {
    if (row.lang === targetLang){
      // find index of theta cutoff that is greater than current theta value
      let scaleScoreIndex = sortedScalescore.length - 1; // use lowest scale score by default
      const isNSE = (row.is_data_insufficient == 1) // equivalent to: prof_level == 'Not Sufficient Evidence to Determine an Outcome'
      
      // alternative report need scale score as well https://bubo.vretta.com/vea/project-management/vretta-project-notes/eqao/admin/-/issues/2542
      //const isAltForm = (row.category == 'ALTERNATIVE')// todo: do not rely on category field use  some thing that is coming more directly from the twtdar
      //if (!isNSE && !isAltForm) {
      if(!isNSE ){
        // const isIndivOverride = (row.is_indiv_override == 1)
        const isRawPerc100 = (row.total_percent == 100)
        /* EXAMPLE

        scale score mapping

        API (>=):
        scale score | theta
        400         |  0.5
        350         |  0.2
        300         |  0
        250         | -0.5
        200         | -1

        take theta = 0.3, scale score 350
        take theta = 0.2, scale score 350
        take theta = -2, scale score UNDEFINED (but defaults to the lowest scale score)
        take theta = 5, scale score 400

        */
        const scaleScoreCutIndex = scaleScoreCutoffs.findIndex(cutoff => row.theta >= cutoff); // theta must be below cutoff, cannot be equal to it
        if (scaleScoreCutIndex !== undefined) {
          scaleScoreIndex = scaleScoreCutIndex
        }
        // enforce minimum bump up rules

        // The original scale scores must be retained in the thetas csv for the override removal purpose
        // The individual override will be applied during the ISR generation process
        // Ref ticket: https://bubo.vretta.com/vea/project-management/vretta-project-notes/eqao/admin/-/issues/3596
        // if (isIndivOverride){
        //   scaleScoreIndex = Math.min(scaleScoreIndex, overrideScaleScoreIndex); // min index is the highest score
        // }

        if (isRawPerc100){
          scaleScoreIndex = Math.min(scaleScoreIndex, maxScaleScoreIndex) // min index is the highest score
        }
        // assign scale score and standard error to theta row
        const ss_record = sortedScalescore[scaleScoreIndex]
        row.scale_score = ss_record.scale_score;
        row.scale_score_se = ss_record.scale_score_se;
      }
    }
  });

  // return applyScaleScoreValidation(thetas, scaleScoreMappings, targetLang)
}

const applyScaleScoreValidation = (thetas: IThetaRow[], scaleScoreMappings: IScaleScore[], targetLang: string) => {
  
  const sortedScalescore = scaleScoreMappings
    .filter((row) => row.lang === targetLang)
    .sort((a, b) => a.scale_score - b.scale_score); // use the inverse sorting to check results

  const scaleValidation: IScaleValidationRow[] = [];
  const scaleScoreMapping: Record<number, IScaleScore> = {};
  const scaleScoreAssignmentErrors: IValidationError[] = [];
  sortedScalescore.forEach((row) => {
    if (row.scale_score) {
      // scaleScoreMapping[row.scale_score] = {
      //   scale_score_se: row.scale_score_se,
      // };
    }
  });

  thetas.forEach((row, i) => {
    const uid = row.uid;
    const testAttemptId = row.test_attempt_id;
    const scaleScore = row.scale_score;
    const theta = row.theta;

    if (scaleScore === undefined || scaleScore <= 0 || scaleScore > sortedScalescore.length) {
      scaleScoreAssignmentErrors.push({ uid, row: i, scale_score: scaleScore || -1, theta, error: 'Scale score out of range' });
    } else {
      const scaleScoreMappingRow = scaleScoreMapping[scaleScore];
      if (!scaleScoreMappingRow) { // confirm that assigned scale score exists
        scaleScoreAssignmentErrors.push({ uid,  row: i, scale_score: scaleScore, theta, error: 'Scale score mapping not found' });
      } 
      else {
        const lang = scaleScoreMappingRow.lang;
        const scaleScoreSe = scaleScoreMappingRow.scale_score_se;
        const scaleScoreIndex = scaleScore - 1;
        const minTheta = scaleValidation[scaleScoreIndex]?.min_theta || theta;
        const maxTheta = scaleValidation[scaleScoreIndex]?.max_theta || theta;

        // scaleValidation[scaleScoreIndex] = { scale_score: scaleScore, lang, scale_score_se: scaleScoreSe, min_theta, max_theta };

        if (theta < minTheta) {
          scaleScoreAssignmentErrors.push({ uid, row: i, scale_score: scaleScore, theta, error: 'Theta below scale score cutoff' });
        } else if (theta > maxTheta) {
          scaleScoreAssignmentErrors.push({ uid, row: i, scale_score: scaleScore, theta, error: 'Theta above scale score cutoff' });
        }
      }
    }
  });


  if (scaleScoreAssignmentErrors.length > 0) {
    console.error('Scale score assignment errors:', scaleScoreAssignmentErrors.length);
  }
  return scaleValidation;
}