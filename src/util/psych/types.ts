export interface IThetaRow {
  uid: number;
  lang: string;
  test_attempt_id?: number;
  is_indiv_override?: number;
  total_percent?: number;
  category?: string;
  is_data_insufficient?: number;
  theta: number;
  temp_maxScaleScoreIndex?: number | string; // temp
  temp_isRawPerc100?: number | string; // temp
  scale_score?: number;
  scale_score_se?: number;
}

export interface IScaleScore {
  lang: string;
  theta: number;
  scale_score: number;
  scale_score_se: number;
}

export interface IScaleValidationRow {
  scale_score: number;
  min_theta: number;
  max_theta: number;
  lang: string;
  scale_score_se: number;
}

export interface IValidationError {
  uid: number;
  test_attempt_id?: number;
  row: number;
  scale_score: number;
  theta: number;
  error: string;
}