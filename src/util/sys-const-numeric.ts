import { Application, Paginated } from "@feathersjs/feathers";
import { Redis } from "ioredis";
import { Errors } from "../errors/general";
import { RedisKeyPrefixes, REDIS_SYS_DATA_EXPIRE } from "../redis/redis";

export const getSysConstNumeric = async (app: Application, FLAG_KEY: string, getValue: boolean=false) => {
  const redis: Redis = app.get('redis');

  if (redis){
    const redisValue = await redis.hget(RedisKeyPrefixes.SysConstantsNumeric, FLAG_KEY);
    if (redisValue) {
      return returnValue(+redisValue, getValue);
    }
  }
    
  const records = <Paginated<any>> await app
    .service('db/read/sys-constants-numeric') //
    .find({query: {key: FLAG_KEY} })

  if (!records.data[0]){
    throw new Errors.BadGateway('INVALID_CONSTANT');
  }
  const value = records.data[0].value as number;

  if (redis){
    redis.pipeline()
      .hset(RedisKeyPrefixes.SysConstantsNumeric, FLAG_KEY, value)
      .expire(RedisKeyPrefixes.SysConstantsNumeric, REDIS_SYS_DATA_EXPIRE, "NX")
      .exec(); // not awaiting
  }

  return returnValue(value, getValue);
}

const returnValue = (value: number, getValue: boolean = false) => {
  if (getValue) {
    return value;
  } else {
    return value === 1;
  }
}