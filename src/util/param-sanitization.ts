export const getPropVals = (data:any, props:string[], allowNull:boolean=true) => {
  const extraction:any = {};
  props.forEach(prop => {
    const val = data[prop];
    const isNull = (val === undefined || val === null);
    if (!isNull || allowNull){
      extraction[prop] = val;
    }
  });
  return extraction
}

export const boolQueryParam = (val:string) => {
  if (!val){
    return false;
  }
  else {
    return !!JSON.parse(val);
  }
}

export const arrUnique = (a:any[]) => [...new Set(a)];

export const arrToAggrMap = <T>(arr:T[], prop:string) => {
  const map:Map<number, T[]> = new Map();
  arr.forEach(r => {
    const key = +(<any>r)[prop]
    let aggr:T[] | undefined = map.get(key);
    if (!aggr){
      aggr = [];
      map.set(key, aggr);
    }
    aggr.push(r);
  });
  return map;
};


export const arrToMap = <T>(arr:T[], prop:string | string[], options?: {isKeyNonNumeric?:boolean, reduceToProp?:string, keyJoinChar?:string}) => {
  const {isKeyNonNumeric, reduceToProp, keyJoinChar} = (options || {})
  const isMultiProp = Array.isArray(prop)
  const map:Map<number|string, T> = new Map();
  arr.forEach((r:any) => {
    let val = (reduceToProp) ? r[reduceToProp] : r;
    let key;
    if (isMultiProp){
      const keyParts = (<string[]>prop).map(propIter => r[propIter])
      key = keyParts.join(keyJoinChar || ';');
    }
    else{
     key = r[prop];
     if (!isKeyNonNumeric){ key = +key; }
    }
    map.set(key, val)
  });
  return map;
};


export const arrToStringMap = <T>(arr:T[], prop:string) => {
  const map:Map<string, T> = new Map();
  arr.forEach(r => map.set(''+(<any>r)[prop], r));
  return map;
};

export const numAsBool = (value:number) : boolean => ((''+<any>value) === '1')
export const boolAsNum = (value:boolean) : number => value ? 1 : 0;

/**
 * Convert array of objects to array of arrays with header at index 0.
 * This function aims to minimize payload size for transmission.
 * @param arrayObjects Array of objects to convert
 * @returns {array} [[header1, header2], [obj1_value1, obj1_value2], ...]
 */
export const convertObjectsToArrayOfArraysWithHeader = (arrayObjects:any[]): any[][] => {
  if (!arrayObjects.length) {
    return []
  }

  const headersFields = Object.keys(arrayObjects[0]); // consistent key order
  const rows = arrayObjects.map(obj =>
    headersFields.map(key => obj[key])
  )

  return [headersFields, ...rows]
}
