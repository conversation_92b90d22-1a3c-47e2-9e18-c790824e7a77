import { Application } from "../declarations";
import { Params } from "@feathersjs/feathers";
import { JWTStrategy } from "@feathersjs/authentication/lib";
import { Errors } from "../errors/general";

export const currentUid = async (app:Application, params:Params) => {
  if (params && params.authentication){
    const authPayload = await app.defaultAuthentication().verifyAccessToken(params.authentication.accessToken);
    return <number> authPayload.uid;
  }
  throw new Errors.NotAuthenticated('NO_PARAM_CURR_UID')
}

export const listMergeUserInfo = async (app:Application, arr:any[], uidProp:string = 'uid', userInfoProp:string = 'user_info' ) => {
  const uids = arr.map((entry: any) => entry[uidProp]);
  const users = <any[]> await app
    .service('db/read/users')
    .db()
    .whereIn('id', uids);
  const userRef = new Map();
  users.forEach(user => {
    userRef.set(user.id, {
      last_name: user.last_name,
      first_name: user.first_name,
      contact_email: user.contact_email,
    })
  })
  arr.forEach(entry => {
    const uid = entry[uidProp];
    entry[userInfoProp] = userRef.get(uid);
  })
}
