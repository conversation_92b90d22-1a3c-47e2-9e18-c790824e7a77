import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import moment from 'moment';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import logger from '../../../../../logger';
import {dbRawRead } from "../../../../../util/db-raw";
import { QCheckInfoLock } from '../../../../public/school-admin/student/student.class';
import analysisItemService from '../../../../public/test-ctrl/assessment-session/analysis-item/analysis-item.service';
import {startUnitTest} from "./unit-tests";

interface Data {}
interface ServiceOptions {}
interface Constraint {
  isSub : boolean,
  type : (value: any, data:any) => Promise<any>,
  data : any,
  targetVal : any,
  bypassNull : boolean,
  warning : boolean,
  errMsg : string
}

export class Validate implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();

    //curl "localhost:3030/private/schools/student/validate"
    //return await startUnitTest(this.app);

    //OEN generation

    const fs = require('fs');

    let writeStream = fs.createWriteStream('OENs.txt');


    for(let i =0; i <1000; i++){
      let result  = '000000001';
      const characters = '012346789';
      const charactersLength = characters.length;
      while(!(await this.C_OEN_DIGIT_CHECK(result,'')) || Number(result) <= 60000000){
        result  = '';
        for ( var j = 0; j < 9; j++ ) {
            result += characters.charAt(Math.floor(Math.random() * charactersLength));
        }
        //this.C_OEN_DIGIT_CHECK(result,'')
      }
      writeStream.write(result, 'utf-8');
      writeStream.write('\n', 'utf-8');
      logger.debug({ oenResult: result });
    }

    // the finish event is emitted when all data has been flushed from the stream
    writeStream.on('finish', () => {
      logger.debug('wrote all data to file');
    });

    // close the stream
    writeStream.end();

    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    //reset environment
    var studentData:any = {};
    var ErrorMessages:string[]  = [];
    var warningMessages:string[] = [];

    //load data
    studentData = data;

    //init and execute constraints
    await this.initAndExecConstraints(studentData,ErrorMessages,warningMessages);

    //return results
    return {"ErrorMessages": ErrorMessages, "warningMessages": warningMessages};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async initAndExecConstraints(studentData:any, ErrorMessages:any, warningMessages:any){
    //run through all fieldname
    var constraints:{[key:number]:Constraint};

    //for all g36 g9 g10 en and fr
    constraints = await this.initConstraints('FirstName',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('LastName',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('StudentOEN',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('SASN',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('Gender',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('LearningFormat',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('IndigenousType',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('FirstLanguage',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('EnrolledOntario',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('OutOfProvinceResidence',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('StatusInCanada',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('Refugee',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('BornOutsideCanada',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('TimeInCanada',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('IEP',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('IPRCExceptionalities',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    // constraints = await this.initConstraints('IPRCBehaviour',studentData);
    // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    // constraints = await this.initConstraints('IPRCAutism',studentData);
    // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    // constraints = await this.initConstraints('IPRCDeaf',studentData);
    // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    // constraints = await this.initConstraints('IPRCBlind',studentData);
    // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    // constraints = await this.initConstraints('IPRCGifted',studentData);
    // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    // constraints = await this.initConstraints('IPRCIntellectual',studentData);
    // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    // constraints = await this.initConstraints('IPRCDevelopmental',studentData);
    // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    // constraints = await this.initConstraints('IPRCMultiple',studentData);
    // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    // constraints = await this.initConstraints('IPRCPhysical',studentData);
    // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    // constraints = await this.initConstraints('IPRCSpeech',studentData);
    // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    // constraints = await this.initConstraints('IPRCLanguage',studentData);
    // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    // constraints = await this.initConstraints('IPRCLearning',studentData);
    // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('SpecPermTemp',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('SpecPermMoved',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    constraints = await this.initConstraints('LanguageLearner',studentData);
    await this.executeConstraints(constraints,ErrorMessages, warningMessages);

    //for g3
    if(studentData['Namespace'] == 'eqao_g3'){
      constraints = await this.initConstraints('DateofBirth_Primary',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('Grade3',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    }

     //for g6
    if(studentData['Namespace'] == 'eqao_g6'){
      constraints = await this.initConstraints('DateofBirth_Junior',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('Grade6',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    }

    //for both g3 and g6
    if(studentData['Namespace'] == 'eqao_g3'||studentData['Namespace'] == 'eqao_g6'){

      constraints = await this.initConstraints('LanguageLearnerEarly',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('StudentType_PJ',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('StudentOEN_PJ',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('Grade',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('DateEnteredSchool_PJ',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('DateEnteredBoard_PJ',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('JrKindergarten',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('SrKindergarten',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('OutOfProvinceResidence_PJ',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccAssistiveTech_PJ',studentData,'Read', '_Reading');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccAssistiveTech_PJ',studentData,'Write', '_Writing');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccAssistiveTech_PJ',studentData,'Math', '_Mathematics');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      // PJ 2024-2025 update: AccBrailleRead, AccBrailleWrite, AccBrailleMath are removed from SDC fields 
      // constraints = await this.initConstraints('AccBraille_PJ',studentData,'Read');
      // await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      // constraints = await this.initConstraints('AccBraille_PJ',studentData,'Write');
      // await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      // constraints = await this.initConstraints('AccBraille_PJ',studentData,'Math');
      // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccAudioVersion_PJ',studentData,'Read');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccAudioVersion_PJ',studentData,'Write');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccAudioVersion_PJ',studentData,'Math');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccSign_PJ',studentData,'Read');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccSign_PJ',studentData,'Write');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccSign_PJ',studentData,'Math');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccScribing_PJ',studentData,'Read');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccScribing_PJ',studentData,'Write');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('AccScribing_PJ',studentData,'Math');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccAltVersion_PJ',studentData,);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      // constraints = await this.initConstraints('AccBraille_2',studentData,'Read');
      // await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      // constraints = await this.initConstraints('AccBraille_2',studentData,'Write');
      // await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      // constraints = await this.initConstraints('AccBraille_2',studentData,'Math');
      // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      // constraints = await this.initConstraints('AccAudioVersion_2',studentData,'Read');
      // await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      // constraints = await this.initConstraints('AccAudioVersion_2',studentData,'Write');
      // await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      // constraints = await this.initConstraints('AccAudioVersion_2',studentData,'Math');
      // await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('Exemption',studentData,'Read');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('Exemption',studentData,'Write');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      constraints = await this.initConstraints('Exemption',studentData,'Math');
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('Exemptions',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('SpecEdNoExpectationReadWrite',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('SpecEdNoExpectationMath',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      //Any records that have a sdc_french_immersion value that is not equal to '#' should be flagged. (Export data check #7)
      constraints = await this.initConstraints('FrenchImmersion_PJ',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    }

     //for g3 g6 and g9 only
    if(studentData['Namespace'] == 'eqao_g3'||studentData['Namespace'] == 'eqao_g6'||studentData['Namespace'] == 'eqao_g9'){
      constraints = await this.initConstraints('ClassCode',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    }

    //for g3 g6 g10 only
    if(studentData['Namespace'] == 'eqao_g3'||studentData['Namespace'] == 'eqao_g6'||studentData['Namespace'] == 'eqao_g10'){
      //All records for OSSLT should have a value of '#' for sdc_french_immersion_extended and sdc_french_immersion. (Export data check #7)
      constraints = await this.initConstraints('FrenchImmersionOrExtended_OSSLT_PJ',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    }

    //for both g9 and g10
    if(studentData['Namespace'] == 'eqao_g9'||studentData['Namespace'] == 'eqao_g10'){
      constraints = await this.initConstraints('StudentOEN_G9_OSSLT',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('StudentType',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('DateEnteredSchool',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('DateEnteredBoard',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('TermFormat',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccAssistiveTech',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccAudioVersion',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccSign',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccAudioResponse',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccScribing',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccAltVersion',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      //All records for OSSLT should have a value of '#' for sdc_french_immersion. (Export data check #7)
      //G9 students should not have any values for sdc_french_immersion. 
      constraints = await this.initConstraints('FrenchImmersion_G9_OSSLT',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    }

    //for g9 only
    if(studentData['Namespace'] == 'eqao_g9'){
      constraints = await this.initConstraints('DateofBirth_G9',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('TermFormat_2',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      //Any records that have a value that is not equal to '#' should be flagged. (Export data check #7)  
      constraints = await this.initConstraints('FrenchImmersionOrExtended_G9',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      // SpecProvBreaks, AccBreaks, AccBraille removed from G9 bussiness validation rule 2024-2025
      // constraints = await this.initConstraints('SpecProvBreaks',studentData);
      // await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      // constraints = await this.initConstraints('AccBreaks',studentData);
      // await this.executeConstraints(constraints,ErrorMessages, warningMessages);
      // constraints = await this.initConstraints('AccBraille',studentData);
      // await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    }

    //for g10 only
    if(studentData['Namespace'] == 'eqao_g10'){
      constraints = await this.initConstraints('DateofBirth_OSSLT',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('Grouping',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('Homeroom',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('EligibilityStatus',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('LevelofStudyLanguage',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('DateOfFTE',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('Graduating',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      //constraints = await this.initConstraints('AccVideotapeResponse');
      //await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('AccOther',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('SpecPermIEP',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);

      constraints = await this.initConstraints('NonParticipationStatus',studentData);
      await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    }

    //en only
    if(studentData['Language'] == 'en'){
      // constraints = await this.initConstraints('ESLELD',studentData);
      // await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    }

    //fr only
    if(studentData['Language'] == 'fr'){
      // constraints = await this.initConstraints('ALFPANA',studentData);
      // await this.executeConstraints(constraints,ErrorMessages, warningMessages);
    }
  }

  async initConstraints(valueName:string, studentData:any, subValueName ='', _subValueName = ''){
    //reset constraints list
    let constraints:{[key:number]:Constraint} = {};

    //Make the variables clear for local use.
    const Namespace = studentData['Namespace'];
    const StudentID =  studentData['StudentID'];
    const Language = studentData["Language"];
    const ClassTermFormat = studentData["ClassTermFormat"]
    const TestWindowEndDate =  studentData['TestWindowEndDate']
    //const IsPenExemption = studentData["IsPenExemption"];
    const IsSASNLogin = studentData["IsSASNLogin"];
    const SchGroupID =   studentData['SchGroupID'];
    const IsPrivate  = studentData['IsPrivate']
    const FirstName =  studentData['FirstName'];
    const LastName =  studentData['LastName'];
    const StudentType =  studentData['StudentType'];
    const StudentOEN =  studentData['StudentOEN'];
    const SASN =  studentData['SASN'];
    const DateofBirth =  studentData['DateofBirth'];
    const Gender =  studentData['Gender'];
    const SchoolClassId =  studentData['SchoolClassId'];
    const ClassCode =  studentData['ClassCode'];
    const LearningFormat =  studentData['LearningFormat'];
    const Grouping =  studentData['Grouping'];
    const Grade =  studentData['Grade'];
    const Homeroom =  studentData['Homeroom'];
    const DateEnteredSchool =  studentData['DateEnteredSchool'];
    const DateEnteredBoard =  studentData['DateEnteredBoard'];
    const JrKindergarten =  studentData['JrKindergarten'];
    const SrKindergarten =  studentData['SrKindergarten'];
    const EligibilityStatus =  studentData['EligibilityStatus'];
    const TermFormat =  studentData['TermFormat'];
    const LevelofStudyLanguage =  studentData['LevelofStudyLanguage'];
    const DateOfFTE =  studentData['DateOfFTE'];
    const Graduating =  studentData['Graduating'];
    const IndigenousType =  studentData['IndigenousType'];
    const FirstLanguage =  studentData['FirstLanguage'];
    const EnrolledOntario =  studentData['EnrolledOntario'];
    const OutOfProvinceResidence =  studentData['OutOfProvinceResidence'];
    const StatusInCanada =  studentData['StatusInCanada'];
    const Refugee =  studentData['Refugee'];
    const BornOutsideCanada =  studentData['BornOutsideCanada'];
    const TimeInCanada =  studentData['TimeInCanada'];
    const IEP =  studentData['IEP'];
    const IPRCExceptionalities =  studentData['IPRCExceptionalities'];
    // const IPRCBehaviour =  studentData['IPRCBehaviour'];
    // const IPRCAutism =  studentData['IPRCAutism'];
    // const IPRCDeaf =  studentData['IPRCDeaf'];
    // const IPRCBlind =  studentData['IPRCBlind'];
    // const IPRCGifted =  studentData['IPRCGifted'];
    // const IPRCIntellectual =  studentData['IPRCIntellectual'];
    // const IPRCDevelopmental =  studentData['IPRCDevelopmental'];
    // const IPRCMultiple =  studentData['IPRCMultiple'];
    // const IPRCPhysical =  studentData['IPRCPhysical'];
    // const IPRCSpeech =  studentData['IPRCSpeech'];
    // const IPRCLanguage =  studentData['IPRCLanguage'];
    // const IPRCLearning =  studentData['IPRCLearning'];
    const AccAssistiveTech =  studentData['AccAssistiveTech'+subValueName];
    const AccAssistiveTechRead =  studentData['AccAssistiveTechRead'];
    const AccAssistiveTechWrite=  studentData['AccAssistiveTechWrite'];
    const AccAssistiveTechMath =  studentData['AccAssistiveTechMath'];
    const AccAssistiveTech_Chrome =  studentData[`AccAssistiveTech_Chrome${_subValueName}`]; // AccAssistiveTech_Chrome (UI order: 0)
    const AccAssistiveTech_Kurz_dl =  studentData[`AccAssistiveTech${_subValueName}_Kurz_dl`]; // AccAssistiveTech_Kurz_dl  (UI order: 1)
    const AccAssistiveTech_Kurz_ext =  studentData[`AccAssistiveTech${_subValueName}_Kurz_ext`]; // AccAssistiveTech_Kurz_ext (UI order: 2)
    const AccAssistiveTech_Nvda =  studentData[`AccAssistiveTech${_subValueName}_Nvda`]; // AccAssistiveTech_Nvda (UI order: 3)
    const AccAssistiveTech_Voiceover =  studentData[`AccAssistiveTech${_subValueName}_Voiceover`]; // AccAssistiveTech_Voiceover (UI order: 4)
    const AccAssistiveTech_Readaloud =  studentData[`AccAssistiveTech${_subValueName}_Readaloud`]; // AccAssistiveTech_Readaloud (UI order: 5)
    const AccAssistiveTech_Jaws =  studentData[`AccAssistiveTech${_subValueName}_Jaws`]; // AccAssistiveTech_Jaws (UI order: 6)
    const AccAssistiveTech_Chromevox =  studentData[`AccAssistiveTech${_subValueName}_Chromevox`]; // AccAssistiveTech_Chromevox (UI order: 7)
    const AccAssistiveTech_Natread =  studentData[`AccAssistiveTech${_subValueName}_Natread`]; // AccAssistiveTech_Natread (UI order: 8)
    const AccAssistiveTech_Custom =  studentData[`AccAssistiveTech${_subValueName}_Custom`]; // AccAssistiveTech_Custom (UI order: 9) 
    // const AccBraille =  studentData['AccBraille'+subValueName];
    // const AccBrailleRead =  studentData['AccBrailleRead'];
    // const AccBrailleWrite=  studentData['AccBrailleWrite'];
    // const AccBrailleMath =  studentData['AccBrailleMath'];
    const AccAudioVersion =  studentData['AccAudioVersion'+subValueName];
    const AccAudioVersionRead =  studentData['AccAudioVersionRead'];
    const AccAudioVersionWrite=  studentData['AccAudioVersionWrite'];
    const AccAudioVersionMath =  studentData['AccAudioVersionMath'];
    const AccBreaks =  studentData['AccBreaks'];
    const AccSign =  studentData['AccSign'+subValueName];
    const AccSignRead =  studentData['AccSignRead'];
    const AccSignWrite=  studentData['AccSignWrite'];
    const AccSignMath =  studentData['AccSignMath'];
    const AccAudioResponse =  studentData['AccAudioResponse'];
    const AccVideotapeResponse =  studentData['AccVideotapeResponse'];
    const AccScribing =  studentData['AccScribing'+subValueName];
    const AccScribingRead =  studentData['AccScribingRead'];
    const AccScribingWrite=  studentData['AccScribingWrite'];
    const AccScribingMath =  studentData['AccScribingMath'];
    const AccAltVersion=  studentData['AccAltVersion'];
    const AccOther =  studentData['AccOther'];
    const Exemption =  studentData['Exemption'+subValueName];
    const ExemptionRead =  studentData['ExemptionRead'];
    const ExemptionWrite =  studentData['ExemptionWrite'];
    const ExemptionMath =  studentData['ExemptionMath'];
    const SpecEdNoExpectationReadWrite =  studentData['SpecEdNoExpectationReadWrite'];
    const SpecEdNoExpectationMath =  studentData['SpecEdNoExpectationMath'];
    const SpecPermIEP =  studentData['SpecPermIEP'];
    const SpecPermTemp =  studentData['SpecPermTemp'];
    const SpecPermMoved =  studentData['SpecPermMoved'];
    const LanguageLearner =  studentData['LanguageLearner'];
    const LanguageLearnerEarly =  studentData['LanguageLearnerEarly'];
    // const ESLELD =  studentData['ESLELD'];
    // const ALFPANA =  studentData['ALFPANA'];
    const SpecProvBreaks =  studentData['SpecProvBreaks'];
    const NonParticipationStatus =  studentData['NonParticipationStatus'];
    const SchoolType = studentData['SchoolType'];
    const FrenchImmersionOrExtended = studentData['FrenchImmersionOrExtended']
    const FrenchImmersion = studentData['FrenchImmersion']

    //init valueName constraint
    switch(valueName){
      case 'FirstName':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,       data: null,   targetVal: FirstName,  bypassNull: false,  warning: false,  errMsg: 'FirstName_NOT_NULL'};
        constraints [2]  = {isSub: false, type: this.C_CHAR_LEN_RANGE, data: [1,50], targetVal: FirstName,  bypassNull: false,  warning: false,  errMsg: 'FirstName_CHAR_LEN_RANGE'};
        break;
      case 'LastName':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,       data: null,   targetVal: LastName,  bypassNull: false,  warning: false,  errMsg: 'LastName_NOT_NULL'};
        constraints [2]  = {isSub: false, type: this.C_CHAR_LEN_RANGE, data: [1,50], targetVal: LastName,  bypassNull: false,  warning: false,  errMsg: 'LastName_CHAR_LEN_RANGE'};
        break;
      case 'StudentType':
        constraints [1]  = {isSub: false, type: this.C_NULL_OK,        data: null,  targetVal: StudentType,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [2]  = {isSub: false, type: this.C_CHAR_LEN_RANGE, data: [1,1], targetVal: StudentType,  bypassNull: true,   warning: false,  errMsg: 'StudentType_CHAR_LEN_RANGE'};
        constraints [3]  = {isSub: false, type: this.C_VAL_RANGE,      data: [1,7], targetVal: StudentType,  bypassNull: true,   warning: false,  errMsg: 'StudentType_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,        data: [11,12], targetVal: constraints,   bypassNull: false,  warning: false, errMsg: 'StudentType_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_VAL_RANGE,      data: [1,1],   targetVal: IsPrivate,        bypassNull: false,  warning: true,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,             data: [13,14], targetVal: constraints,  bypassNull: false,  warning: true,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_VAL_RANGE,      data: [3,4],   targetVal: StudentType,  bypassNull: false,  warning: true,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_NULL,        data: null,   targetVal: StudentType,  bypassNull: false,  warning: true,  errMsg: ''};
        break;
      case 'StudentType_PJ':
        constraints [1]  = {isSub: false, type: this.C_NULL_OK,        data: null,  targetVal: StudentType,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [2]  = {isSub: false, type: this.C_CHAR_LEN_RANGE, data: [1,1], targetVal: StudentType,  bypassNull: true,   warning: false,  errMsg: 'StudentType_CHAR_LEN_RANGE'};
        constraints [3]  = {isSub: false, type: this.C_VAL_RANGE,      data: [1,2], targetVal: StudentType,  bypassNull: true,   warning: false,  errMsg: 'StudentType_VAL_RANGE'};
        //StudentType should be # or 0 for private schools. If this is not true, it should be flagged for Vretta's attention.(Export data check #56)
        constraints [10] = {isSub: false, type: this.C_IF_THEN,        data: [11,12], targetVal: constraints,   bypassNull: false,  warning: false, errMsg: 'StudentType_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_VAL_RANGE,      data: [1,1],   targetVal: IsPrivate,        bypassNull: false,  warning: true,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_IS_NULL,        data: null,    targetVal: StudentType,  bypassNull: false,  warning: true,  errMsg: ''};
        break;
      case 'StudentOEN':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,         data: null,                              targetVal: StudentOEN,          bypassNull: false,  warning: false, errMsg: 'StudentOEN_NOT_NULL'};
        constraints [2]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,   data: [9,9],                             targetVal: StudentOEN,          bypassNull: false,  warning: false, errMsg: 'StudentOEN_CHAR_LEN_RANGE'};
        constraints [3]  = {isSub: false, type: this.C_CHAR_PATTERN,     data: /^\d+$/,                           targetVal: StudentOEN,          bypassNull: false,  warning: false, errMsg: 'StudentOEN_CHAR_PATTERN'};
        constraints [18] = {isSub: false, type: this.C_OEN_UNIQUE,       data: {StudentID, SchGroupID, Namespace},targetVal: StudentOEN,          bypassNull: false,  warning: false, errMsg: 'StudentOEN_OEN_UNIQUE2'};
        constraints [20] = {isSub: false, type: this.C_OR,               data: [21,24],                           targetVal: constraints,         bypassNull: false,  warning: false, errMsg: 'StudentOEN_OEN_UNIQUE1'};
        constraints [21] = {isSub: true,  type: this.C_AND,              data: [22,23],                           targetVal: constraints,         bypassNull: false,  warning: false, errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_VAL_RANGE,        data: [1,4],                             targetVal: StudentType,         bypassNull: false,  warning: false, errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_VAL_RANGE,        data: [0,0],                             targetVal: StudentOEN,          bypassNull: false,  warning: false, errMsg: ''};
        constraints [24] = {isSub: true,  type: this.C_OEN_DIGIT_CHECK,  data: null,                              targetVal: StudentOEN,          bypassNull: false,  warning: false, errMsg: ''};
        break;
      case 'StudentOEN_G9_OSSLT':
        constraints [10] = {isSub: false, type: this.C_OR,               data: [11,12,15],                        targetVal: constraints,         bypassNull: false,  warning: false, errMsg: 'StudentOEN_VAL_RANGE'};
        constraints [11] = {isSub: true,  type: this.C_VAL_RANGE,        data: [60000000,999999999],              targetVal: StudentOEN,          bypassNull: false,  warning: false, errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_AND,              data: [13,14],                           targetVal: constraints,         bypassNull: false,  warning: false, errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_VAL_RANGE,        data: [0,0],                             targetVal: StudentOEN,          bypassNull: false,  warning: false, errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_SASN_LOGIN,    data: null,                              targetVal: IsSASNLogin,         bypassNull: false,  warning: false, errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_AND,              data: [16,17],                           targetVal: constraints,         bypassNull: false,  warning: false, errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_VAL_RANGE,        data: [1,4],                             targetVal: StudentType,         bypassNull: false,  warning: false, errMsg: ''};
        constraints [17] = {isSub: true,  type: this.C_VAL_RANGE,        data: [0,0],                             targetVal: StudentOEN,          bypassNull: false,  warning: false, errMsg: ''};
        break;
      case 'StudentOEN_PJ':
        constraints [10] = {isSub: false, type: this.C_OR,               data: [11,12,15,20],                        targetVal: constraints,         bypassNull: false,  warning: false, errMsg: 'StudentOEN_VAL_RANGE'};
        constraints [11] = {isSub: true,  type: this.C_VAL_RANGE,        data: [60000000,999999999],              targetVal: StudentOEN,          bypassNull: false,  warning: false, errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_AND,              data: [13,14],                           targetVal: constraints,         bypassNull: false,  warning: false, errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_VAL_RANGE,        data: [0,0],                             targetVal: StudentOEN,          bypassNull: false,  warning: false, errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_SASN_LOGIN,    data: null,                              targetVal: IsSASNLogin,         bypassNull: false,  warning: false, errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_AND,              data: [16,17],                           targetVal: constraints,         bypassNull: false,  warning: false, errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_VAL_RANGE,        data: [1,2],                             targetVal: StudentType,         bypassNull: false,  warning: false, errMsg: ''};
        constraints [17] = {isSub: true,  type: this.C_VAL_RANGE,        data: [0,0],                             targetVal: StudentOEN,          bypassNull: false,  warning: false, errMsg: ''};
        constraints [20] = {isSub: true,  type: this.C_AND,              data: [21,22],                           targetVal: constraints,         bypassNull: false,  warning: false, errMsg: ''};
        constraints [21] = {isSub: true,  type: this.C_CHAR_PATTERN,     data: /^non_inspected$/,                 targetVal: SchoolType,         bypassNull: false,  warning: false, errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_VAL_RANGE,        data: [0,0],                             targetVal: StudentOEN,          bypassNull: false,  warning: false, errMsg: ''};
        break;
      case 'SASN':
        constraints [1]  = {isSub: false, type: this.C_NULL_OK,        data: null,             targetVal: SASN,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [2]  = {isSub: false, type: this.C_CHAR_LEN_RANGE, data: [1,9],            targetVal: SASN,          bypassNull: true,   warning: false,  errMsg: 'SASN_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,        data: [11,14],          targetVal: constraints,   bypassNull: false,  warning: false,  errMsg: 'SASN_NOT_NULL'};
        constraints [11] = {isSub: true,  type: this.C_OR,             data: [12,13],          targetVal: constraints,   bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_VAL_RANGE,      data: [1,1],            targetVal: IsSASNLogin,   bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_VAL_RANGE,      data: [0,0],            targetVal: StudentOEN,    bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_AND,            data: [15,16],          targetVal: constraints,   bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,       data: null,             targetVal: SASN,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_CHAR_PATTERN,   data: /^[A-Za-z0-9]+$/, targetVal: SASN,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [20] = {isSub: false, type: this.C_IF_THEN,        data: [21,24],          targetVal: constraints,   bypassNull: false,  warning: false,  errMsg: 'SASN_UNIQUE'};
        constraints [21] = {isSub: true,  type: this.C_OR,             data: [22,23],          targetVal: constraints,   bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_VAL_RANGE,      data: [1,1],            targetVal: IsSASNLogin,   bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_VAL_RANGE,      data: [0,0],            targetVal: StudentOEN,    bypassNull: false,  warning: false,  errMsg: ''};
        constraints [24] = {isSub: true,  type: this.C_SASN_UNIQUE,    data: {StudentID, SchGroupID, Namespace},  targetVal: SASN,   bypassNull: false,  warning: false, errMsg: ''};
        break;
      case 'DateofBirth_G9':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,          data: null,  targetVal: DateofBirth,  bypassNull: false,  warning: false, errMsg: 'DateofBirth_NOT_NULL'};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,    data: [8,8], targetVal: DateofBirth,  bypassNull: false,  warning: false, errMsg: 'DateofBirth_CHAR_LEN_RANGE'};
        //The date of the dob field must be YYYYMMDD (Export data check #22)
        constraints [10] = {isSub: false, type: this.C_DATE_VALIDATE,     data: null,  targetVal: DateofBirth,  bypassNull: false,  warning: false, errMsg: 'DateofBirth_DATE_VALIDATE'};
        //The difference in years between the current date and the 'DateofBirth' field should not be less than 13 for G9. (Export data check #23)(OSSLT BR Role No.12)
        constraints [11] = {isSub: false, type: this.C_DATE_DIFF_GREATER, data: 13,    targetVal: DateofBirth,  bypassNull: false,  warning: true,  errMsg: 'DateofBirth_DATE_DIFF_GREATER'};
        constraints [12] = {isSub: false, type: this.C_DATE_DIFF_SMALLER, data: 17,    targetVal: DateofBirth,  bypassNull: false,  warning: true,  errMsg: 'DateofBirth_DATE_DIFF_SMALLER'};
        break;
      case 'DateofBirth_OSSLT':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,          data: null,  targetVal: DateofBirth,  bypassNull: false,  warning: false, errMsg: 'DateofBirth_NOT_NULL'};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,    data: [8,8], targetVal: DateofBirth,  bypassNull: false,  warning: false, errMsg: 'DateofBirth_CHAR_LEN_RANGE'};
        //The date of the dob field must be YYYYMMDD (Export data check #22)
        constraints [10] = {isSub: false, type: this.C_DATE_VALIDATE,     data: null,  targetVal: DateofBirth,  bypassNull: false,  warning: false, errMsg: 'DateofBirth_DATE_VALIDATE'};
        //The difference in years between the current date and the 'DateofBirth' field should not be less than 14 for OSSLT. (Export data check #23)(OSSLT BR Role No.22)
        constraints [11] = {isSub: false, type: this.C_DATE_DIFF_GREATER, data: 14,    targetVal: DateofBirth,  bypassNull: false,  warning: true,  errMsg: 'DateofBirth_DATE_DIFF_GREATER_OSSLT'};
        constraints [12] = {isSub: false, type: this.C_DATE_DIFF_SMALLER, data: 17,    targetVal: DateofBirth,  bypassNull: false,  warning: true,  errMsg: 'DateofBirth_DATE_DIFF_SMALLER'};
        break;
      case 'DateofBirth_Primary':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,          data: null,  targetVal: DateofBirth,  bypassNull: false,  warning: false, errMsg: 'DateofBirth_NOT_NULL'};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,    data: [8,8], targetVal: DateofBirth,  bypassNull: false,  warning: false, errMsg: 'DateofBirth_CHAR_LEN_RANGE'};
        //The date of the dob field must be YYYYMMDD (Export data check #22)
        constraints [10] = {isSub: false, type: this.C_DATE_VALIDATE,     data: null,  targetVal: DateofBirth,  bypassNull: false,  warning: false, errMsg: 'DateofBirth_DATE_VALIDATE'};
        // Grade 3 - The difference in years between the current date and the “DateofBirth” should be between 8 and 10 (2024-2025) (use 11 in the code due to 10.xx yaers old is consider 10 years old)
        // https://www.notion.so/vretta/2024-2025-SDC-Update-PJ-b96a81f0756d4ea2800571dfd6ed0702
        constraints [11] = {isSub: false, type: this.C_DATE_DIFF_GREATER, data: 8,    targetVal: DateofBirth,  bypassNull: false,  warning: true,  errMsg: 'DateofBirth_DATE_DIFF_GREATER_PJ'};
        constraints [12] = {isSub: false, type: this.C_DATE_DIFF_SMALLER, data: 11,    targetVal: DateofBirth,  bypassNull: false,  warning: true,  errMsg: 'DateofBirth_DATE_DIFF_SMALLER_PJ'};
        break;
      case 'DateofBirth_Junior':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,          data: null,  targetVal: DateofBirth,  bypassNull: false,  warning: false, errMsg: 'DateofBirth_NOT_NULL'};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,    data: [8,8], targetVal: DateofBirth,  bypassNull: false,  warning: false, errMsg: 'DateofBirth_CHAR_LEN_RANGE'};
        //The date of the dob field must be YYYYMMDD (Export data check #22)
        constraints [10] = {isSub: false, type: this.C_DATE_VALIDATE,     data: null,  targetVal: DateofBirth,  bypassNull: false,  warning: false, errMsg: 'DateofBirth_DATE_VALIDATE'};
        // Grade 6 - The difference in years between the current date and the “DateofBirth” should be between 11 and 13 (use 14 in the code due to 13.xx yaers old is consider 13 years old)
        // https://www.notion.so/vretta/2024-2025-SDC-Update-PJ-b96a81f0756d4ea2800571dfd6ed0702
        constraints [11] = {isSub: false, type: this.C_DATE_DIFF_GREATER, data: 11,    targetVal: DateofBirth,  bypassNull: false,  warning: true,  errMsg: 'DateofBirth_DATE_DIFF_GREATER_PJ'};
        constraints [12] = {isSub: false, type: this.C_DATE_DIFF_SMALLER, data: 14,    targetVal: DateofBirth,  bypassNull: false,  warning: true,  errMsg: 'DateofBirth_DATE_DIFF_SMALLER_PJ'};
        break;
      case 'Gender':
        constraints [1]   = {isSub: false, type: this.C_NOT_NULL,       data: null,    targetVal: Gender,           bypassNull: false,   warning: false,  errMsg: 'Gender_NOT_NULL'};
        constraints [3]   = {isSub: false, type: this.C_CHAR_LEN_RANGE, data: [1,1],   targetVal: Gender,           bypassNull: false,   warning: false,  errMsg: 'Gender_CHAR_LEN_RANGE'};
        constraints [10]  = {isSub: false, type: this.C_OR,             data: [11,12], targetVal: constraints, bypassNull: false,   warning: false,  errMsg: 'Gender_VAL_RANGE'};
        constraints [11]  = {isSub: true,  type: this.C_VAL_RANGE,      data: [1,2],   targetVal: Gender,           bypassNull: false,   warning: false,  errMsg: ''};
        constraints [12]  = {isSub: true,  type: this.C_VAL_RANGE,      data: [9,9],   targetVal: Gender,           bypassNull: false,   warning: false,  errMsg: ''};
        //constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,      data: [1,2], targetVal: Gender,  bypassNull: false,   warning: false,  errMsg: 'Gender_VAL_RANGE'};
        break;
      case 'ClassCode':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,       data: null,   targetVal: ClassCode,  bypassNull: false,   warning: false,  errMsg: 'ClassCode_NOT_NULL'};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE, data: [1,20], targetVal: ClassCode,  bypassNull: false,   warning: false,  errMsg: 'ClassCode_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VALIDE_CLASS,   data: null,   targetVal: ClassCode,  bypassNull: false,   warning: false,  errMsg: 'ClassCode_VALIDE_CLASS'};
        //constraints [5]  = {isSub: false, type: this.C_VALIDE_CLASS_2, data: null, targetVal: ClassCode,  bypassNull: false,   warning: false,  errMsg: 'ClassCode_VALIDE_CLASS_2'};
        constraints [6]  = {isSub: false, type: this.C_VALIDE_CLASS_ID,  data: null,  targetVal: SchoolClassId,  bypassNull: false,   warning: false,  errMsg: 'ClassCode_VALIDE_CLASS'};
        break;
      case 'LearningFormat':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,       data: null,  targetVal: LearningFormat,  bypassNull: false,   warning: false,  errMsg: 'LearningFormat_NOT_NULL'};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE, data: [1,1], targetVal: LearningFormat,  bypassNull: false,   warning: false,  errMsg: 'LearningFormat_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,      data: [1,3], targetVal: LearningFormat,  bypassNull: false,   warning: false,  errMsg: 'LearningFormat_VAL_RANGE'};
        break;
      case 'Grouping':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,        data: null,   targetVal: Grouping,  bypassNull: false,   warning: false,  errMsg: 'Grouping_NOT_NULL'};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,25], targetVal: Grouping,  bypassNull: false,   warning: false,  errMsg: 'Grouping_CHAR_LEN_RANGE'};
        constraints [13] = {isSub: false, type: this.C_VALIDE_GRPING,   data: null,   targetVal: Grouping,  bypassNull: false,   warning: false,  errMsg: 'Grouping_VALIDE_GRPING'};
        constraints [14] = {isSub: false, type: this.C_VALIDE_GRPING_2, data: null,   targetVal: Grouping,  bypassNull: false,   warning: false,  errMsg: 'Grouping_VALIDE_GRPING_2'};
        constraints [15] = {isSub: false, type: this.C_VALIDE_CLASS_ID, data: null,   targetVal: SchoolClassId,  bypassNull: false,   warning: false,  errMsg: 'Grouping_VALIDE_GRPING_2'};
        break;
      case 'Grade':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,        data: null,   targetVal: Grade,           bypassNull: false,   warning: false,  errMsg: 'Grade_NOT_NULL'};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],  targetVal: Grade,           bypassNull: false,   warning: false,  errMsg: 'Grade_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_OR,             data: [5,6],   targetVal: constraints,     bypassNull: false,   warning: false,  errMsg: 'Grade_VAL_RANGE'};
        constraints [5]  = {isSub: true,  type: this.C_VAL_RANGE,      data: [3,3],   targetVal: Grade,           bypassNull: false,   warning: false,  errMsg: ''};
        constraints [6]  = {isSub: true,  type: this.C_VAL_RANGE,      data: [6,6],   targetVal: Grade,           bypassNull: false,   warning: false,  errMsg: ''};
        break;
      case 'Grade3':
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,      data: [3,3], targetVal: Grade,  bypassNull: false,   warning: false,  errMsg: 'Grade3_VAL_RANGE'};
        break;
      case 'Grade6':
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,      data: [6,6], targetVal: Grade,  bypassNull: false,   warning: false,  errMsg: 'Grade6_VAL_RANGE'};
        break;
      case 'Homeroom':
        constraints [1]  = {isSub: false, type: this.C_NULL_OK,         data: null,   targetVal: Homeroom,  bypassNull: false,   warning: false,  errMsg: 'Homeroom_NOT_NULL'};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,25], targetVal: Homeroom,  bypassNull: true,    warning: false,  errMsg: 'Homeroom_CHAR_LEN_RANGE'};
        constraints [14] = {isSub: false, type: this.C_VALIDE_HOMEROOM, data: null,   targetVal: Homeroom,  bypassNull: true,    warning: false,  errMsg: 'Homeroom_VALIDE_HOMEROOM'};
        break;
      case 'DateEnteredSchool':
        constraints [1]  = {isSub: false, type: this.C_NULL_OK,           data: null,  targetVal: DateEnteredSchool,  bypassNull: false,  warning: false, errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,    data: [8,8], targetVal: DateEnteredSchool,  bypassNull: true,   warning: false, errMsg: 'DateEnteredSchool_CHAR_LEN_RANGE'};
        //DateEnteredSchool, DateEnteredBoard should be in YYYYMMDD format just like date of birth (Export data check #54)
        constraints [10] = {isSub: false, type: this.C_DATE_VALIDATE,     data: null,  targetVal: DateEnteredSchool,  bypassNull: true,   warning: false, errMsg: 'DateEnteredSchool_DATE_VALIDATE'};
        //If a date is present in DateEnteredSchool or DateEnteredBoard, it must be in the range of the current date minus 10 years for PJ and 17 years for G9 and OSSLT.(Export data check #39)(OSSLT BR Role No.59)(G9 BR Role No.60)
        constraints [12] = {isSub: false, type: this.C_DATE_DIFF_SMALLER, data: 17,    targetVal: DateEnteredSchool,  bypassNull: true,   warning: true,  errMsg: 'DateEnteredSchool_DATE_DIFF_SMALLER'};
        //The "DateEnteredBoard" or "DateEnteredSchool" should not be earlier than the "DateOfBirth." (OSSLT BR Role No.59d)(G9 BR Role No.60d)
        constraints [13] = {isSub: false, type: this.C_DATE_GREATER,      data: DateofBirth,    targetVal: DateEnteredSchool,  bypassNull: true,   warning: false,  errMsg: 'DateEnteredSchool_DATE_SMALLER_THAN_BIRTHDAY'};
        //Dates for "DateEnteredBoard" or "DateEnteredSchool" should not be later than the current year of the assessment. (OSSLT BR Role No.59c)(G9 BR Role No.60c)
        constraints [14] = {isSub: false, type: this.C_DATE_SMALLER,      data: TestWindowEndDate,    targetVal: DateEnteredSchool,  bypassNull: true,   warning: false,  errMsg: 'DateEnteredSchool_DATE_SMALLER_THAN_TW_END_DATE'};
        //Dates for "DateEnteredBoard" or "DateEnteredSchool" should not be equal to the "DateOfBirth." (OSSLT BR Role No.59d)(G9 BR Role No.60d)
        constraints [15] = {isSub: false, type: this.C_NOT_EQUAL,         data: DateofBirth,          targetVal: DateEnteredSchool,  bypassNull: true,   warning: false,  errMsg: 'DateEnteredSchool_DATE_SMALLER_THAN_BIRTHDAY'};
        break;
      case 'DateEnteredSchool_PJ':
        constraints [1]  = {isSub: false, type: this.C_NULL_OK,           data: null,  targetVal: DateEnteredSchool,  bypassNull: false,  warning: false, errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,    data: [8,8], targetVal: DateEnteredSchool,  bypassNull: true,   warning: false, errMsg: 'DateEnteredSchool_CHAR_LEN_RANGE'};
        //DateEnteredSchool, DateEnteredBoard should be in YYYYMMDD format just like date of birth (Export data check #54)
        constraints [10] = {isSub: false, type: this.C_DATE_VALIDATE,     data: null,  targetVal: DateEnteredSchool,  bypassNull: true,   warning: false, errMsg: 'DateEnteredSchool_DATE_VALIDATE'};
        //If a date is present in DateEnteredSchool or DateEnteredBoard, it must be in the range of the current date minus 10 years for PJ and 17 years for G9 and OSSLT.(Export data check #39)(PJ BR Role No.38)
        constraints [12] = {isSub: false, type: this.C_DATE_DIFF_SMALLER, data: 10,    targetVal: DateEnteredSchool,  bypassNull: false,  warning: true, errMsg: 'DateEnteredSchool_DATE_DIFF_SMALLER2'};
        constraints [13] = {isSub: false, type: this.C_DATE_GREATER,      data: DateofBirth,    targetVal: DateEnteredSchool,  bypassNull: true,   warning: false,  errMsg: 'DateEnteredSchool_DATE_SMALLER_THAN_BIRTHDAY'};
        //Dates for "DateEnteredBoard" or "DateEnteredSchool" should not be later than the current year of the assessment. (PJ BR Role No.38c)
        constraints [14] = {isSub: false, type: this.C_DATE_SMALLER,      data: TestWindowEndDate,    targetVal: DateEnteredSchool,  bypassNull: true,   warning: false,  errMsg: 'DateEnteredSchool_DATE_SMALLER_THAN_TW_END_DATE'};
        //Dates for "DateEnteredBoard" or "DateEnteredSchool" should not be equal to the "DateOfBirth." (2024-2025 PJ BR no. 38d)
        constraints [15] = {isSub: false, type: this.C_NOT_EQUAL,         data: DateofBirth,          targetVal: DateEnteredSchool,  bypassNull: true,   warning: false,  errMsg: 'DateEnteredSchool_DATE_SMALLER_THAN_BIRTHDAY'};
        break;
      case 'DateEnteredBoard':
        constraints [1]  = {isSub: false, type: this.C_NULL_OK,           data: null,               targetVal: DateEnteredBoard,  bypassNull: false,  warning: false, errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,    data: [8,8],              targetVal: DateEnteredBoard,  bypassNull: true,   warning: false, errMsg: 'DateEnteredBoard_CHAR_LEN_RANGE'};
        //DateEnteredSchool, DateEnteredBoard should be in YYYYMMDD format just like date of birth (Export data check #54)
        constraints [10] = {isSub: false, type: this.C_DATE_VALIDATE,     data: null,               targetVal: DateEnteredBoard,  bypassNull: true,   warning: false, errMsg: 'DateEnteredBoard_DATE_VALIDATE'};
        //If a date is present in DateEnteredSchool or DateEnteredBoard, it must be in the range of the current date minus 10 years for PJ and 17 years for G9 and OSSLT.(Export data check #39)(OSSLT BR Role No.59)(G9 BR Role No.60)        
        constraints [12] = {isSub: false, type: this.C_DATE_DIFF_SMALLER, data: 17,                 targetVal: DateEnteredBoard,  bypassNull: false,  warning: true,  errMsg: 'DateEnteredBoard_DATE_DIFF_SMALLER'};
        constraints [13] = {isSub: false, type: this.C_DATE_SMALLER,      data: DateEnteredSchool,  targetVal: DateEnteredBoard,  bypassNull: false,  warning: false, errMsg: 'DateEnteredBoard_DATE_GREATER'};
        //The "DateEnteredBoard" or "DateEnteredSchool" should not be earlier than the "DateOfBirth." (OSSLT BR Role No.59d)(G9 BR Role No.60d)
        constraints [14] = {isSub: false, type: this.C_DATE_GREATER,      data: DateofBirth,        targetVal: DateEnteredBoard,  bypassNull: true,   warning: false,  errMsg: 'DateEnteredBoard_DATE_SMALLER_THAN_BIRTHDAY'};
        //Dates for "DateEnteredBoard" or "DateEnteredSchool" should not be later than the current year of the assessment. (OSSLT BR Role No.59c)(G9 BR Role No.60c)
        constraints [15] = {isSub: false, type: this.C_DATE_SMALLER,      data: TestWindowEndDate,    targetVal: DateEnteredBoard,  bypassNull: true,   warning: false,  errMsg: 'DateEnteredBoard_DATE_SMALLER_THAN_TW_END_DATE'};
        //Dates for "DateEnteredBoard" or "DateEnteredSchool" should not be equal to the "DateOfBirth." (OSSLT BR Role No.59d)(G9 BR Role No.60d)
        constraints [16] = {isSub: false, type: this.C_NOT_EQUAL,         data: DateofBirth,        targetVal: DateEnteredBoard,  bypassNull: true,   warning: false,  errMsg: 'DateEnteredBoard_DATE_SMALLER_THAN_BIRTHDAY'};
        break;
      case 'DateEnteredBoard_PJ':
        constraints [1]  = {isSub: false, type: this.C_NULL_OK,           data: null,               targetVal: DateEnteredBoard,  bypassNull: false,  warning: false, errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,    data: [8,8],              targetVal: DateEnteredBoard,  bypassNull: true,   warning: false, errMsg: 'DateEnteredBoard_CHAR_LEN_RANGE'};
        //DateEnteredSchool, DateEnteredBoard should be in YYYYMMDD format just like date of birth (Export data check #54)
        constraints [10] = {isSub: false, type: this.C_DATE_VALIDATE,     data: null,               targetVal: DateEnteredBoard,  bypassNull: true,   warning: false, errMsg: 'DateEnteredBoard_DATE_VALIDATE'};
        //If a date is present in DateEnteredSchool or DateEnteredBoard, it must be in the range of the current date minus 10 years for PJ and 17 years for G9 and OSSLT.(Export data check #39)(PJ BR Role No.38)        
        constraints [12]  = {isSub: false, type: this.C_DATE_DIFF_SMALLER, data: 10,                targetVal: DateEnteredBoard,  bypassNull: false,  warning: true, errMsg: 'DateEnteredBoard_DATE_DIFF_SMALLER2'};
        constraints [13] = {isSub: false, type: this.C_DATE_SMALLER,      data: DateEnteredSchool,  targetVal: DateEnteredBoard,  bypassNull: false,  warning: false, errMsg: 'DateEnteredBoard_DATE_GREATER'};
        constraints [14] = {isSub: false, type: this.C_DATE_GREATER,      data: DateofBirth,        targetVal: DateEnteredBoard,  bypassNull: true,   warning: false,  errMsg: 'DateEnteredBoard_DATE_SMALLER_THAN_BIRTHDAY'};
        //Dates for "DateEnteredBoard" or "DateEnteredSchool" should not be later than the current year of the assessment. (PJ BR Role No.38c)
        constraints [15] = {isSub: false, type: this.C_DATE_SMALLER,      data: TestWindowEndDate,    targetVal: DateEnteredBoard,  bypassNull: true,   warning: false,  errMsg: 'DateEnteredBoard_DATE_SMALLER_THAN_TW_END_DATE'};
        //Dates for "DateEnteredBoard" or "DateEnteredSchool" should not be equal to the "DateOfBirth." (2024-2025 PJ BR rule no.38d)
        constraints [16] = {isSub: false, type: this.C_NOT_EQUAL,         data: DateofBirth,        targetVal: DateEnteredBoard,  bypassNull: true,   warning: false,  errMsg: 'DateEnteredBoard_DATE_SMALLER_THAN_BIRTHDAY'};
        break;
      case 'JrKindergarten':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,    targetVal: JrKindergarten,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],   targetVal: JrKindergarten,  bypassNull: true,   warning: false,  errMsg: 'JrKindergarten_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,2],   targetVal: JrKindergarten,  bypassNull: true,   warning: false,  errMsg: 'JrKindergarten_VAL_RANGE'};
        break;
      case 'SrKindergarten':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,    targetVal: SrKindergarten,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],   targetVal: SrKindergarten,  bypassNull: true,   warning: false,  errMsg: 'SrKindergarten_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,2],   targetVal: SrKindergarten,  bypassNull: true,   warning: false,  errMsg: 'SrKindergarten_VAL_RANGE'};
        break;
      case 'EligibilityStatus':
        constraints [1]  = {isSub: false, type: this.C_NOT_NULL,        data: null,    targetVal: EligibilityStatus,  bypassNull: false,  warning: false, errMsg: 'EligibilityStatus_NOT_NULL'};
        constraints [2]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],   targetVal: EligibilityStatus,  bypassNull: false,  warning: false, errMsg: 'EligibilityStatus_CHAR_LEN_RANGE'};
        constraints [3]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,2],   targetVal: EligibilityStatus,  bypassNull: false,  warning: false, errMsg: 'EligibilityStatus_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12], targetVal: constraints,   bypassNull: false,  warning: false, errMsg: 'EligibilityStatus_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_VAL_RANGE,       data: [3,4],   targetVal: StudentType,        bypassNull: false,  warning: true,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_VAL_RANGE,       data: [2,2],   targetVal: EligibilityStatus,  bypassNull: false,  warning: true,  errMsg: ''};
        break;
      case 'TermFormat':
        constraints [2]  = {isSub: false, type: this.C_NOT_NULL,        data: null,    targetVal: TermFormat,  bypassNull: false,  warning: false, errMsg: 'TermFormat_NOT_NULL'};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],   targetVal: TermFormat,  bypassNull: true,   warning: false, errMsg: 'TermFormat_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,5],   targetVal: TermFormat,  bypassNull: true,   warning: false, errMsg: 'TermFormat_VAL_RANGE'};
        break;
      case 'TermFormat_2':
        //constraints [1]  = {isSub: false, type: this.C_NOT_NULL,        data: null,    targetVal: TermFormat,  bypassNull: false,  warning: false, errMsg: 'TermFormat_NOT_NULL'};
        constraints [5]  = {isSub: false, type: this.C_EQUAL,          data: ClassTermFormat, targetVal: TermFormat,  bypassNull: false,   warning: false,  errMsg: 'TermFormat_VAL_1'};
        break;  
      case 'LevelofStudyLanguage':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,    targetVal: LevelofStudyLanguage,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],   targetVal: LevelofStudyLanguage,  bypassNull: true,   warning: false,  errMsg: 'LevelofStudyLanguage_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,5],   targetVal: LevelofStudyLanguage,  bypassNull: true,   warning: false,  errMsg: 'LevelofStudyLanguage_VAL_RANGE'};
        //If “EligibiltyStatus” = 1 (first-time eligible), “LevelofStudyLanguage” must not be null (OSSLT BR Role No.24)
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12], targetVal: constraints,           bypassNull: false,  warning: false,  errMsg: 'LevelofStudyLanguage_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],   targetVal: EligibilityStatus,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_NOT_NULL,        data: null,    targetVal: LevelofStudyLanguage,  bypassNull: false,  warning: false,  errMsg: ''};
        //If “EligibiltyStatus” = 2 (first-time eligible), “LevelofStudyLanguage” must be null (OSSLT BR Role No.24)
        constraints [20] = {isSub: false, type: this.C_IF_THEN,         data: [21,22], targetVal: constraints,           bypassNull: false,  warning: false,  errMsg: 'LevelofStudyLanguage_VAL_VALIDATE_2'};
        constraints [21] = {isSub: true,  type: this.C_VAL_RANGE,       data: [2,2],   targetVal: EligibilityStatus,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,    targetVal: LevelofStudyLanguage,  bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'DateOfFTE':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,    targetVal: DateOfFTE,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [2,2],   targetVal: DateOfFTE,          bypassNull: true,   warning: false,  errMsg: 'DateOfFTE_CHAR_LEN_RANGE'};
        //DateOfFTE value range to [19-22]   (OSSLT BR Role No.24)
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [20,23], targetVal: DateOfFTE,          bypassNull: true,   warning: false,  errMsg: 'DateOfFTE_VAL_RANGE'};
        //If “EligibiltyStatus” = 2 (first-time eligible), “DateOfFTE” must not be null (OSSLT BR Role No.24)
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12], targetVal: constraints,        bypassNull: false,  warning: false,  errMsg: 'DateOfFTE_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_VAL_RANGE,       data: [2,2],   targetVal: EligibilityStatus,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_NOT_NULL,        data: null,    targetVal: DateOfFTE,          bypassNull: false,  warning: false,  errMsg: ''};
        //If “EligibiltyStatus” = 1 (first-time eligible), “DateOfFTE” must be null (OSSLT BR Role No.24)
        constraints [20] = {isSub: false, type: this.C_IF_THEN,         data: [21,22], targetVal: constraints,        bypassNull: false,  warning: false,  errMsg: 'DateOfFTE_VAL_VALIDATE_2'};
        constraints [21] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],   targetVal: EligibilityStatus,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,    targetVal: DateOfFTE,          bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'Graduating':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,    targetVal: Graduating,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],   targetVal: Graduating,         bypassNull: true,   warning: false,  errMsg: 'Graduating_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],   targetVal: Graduating,         bypassNull: true,   warning: false,  errMsg: 'Graduating_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12], targetVal: constraints,        bypassNull: false,  warning: false,  errMsg: 'Graduating_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],   targetVal: Graduating,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_VAL_RANGE,       data: [2,2],   targetVal: EligibilityStatus,  bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'IndigenousType':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,    targetVal: IndigenousType,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],   targetVal: IndigenousType,  bypassNull: true,   warning: false,  errMsg: 'IndigenousType_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [2,4],   targetVal: IndigenousType,  bypassNull: true,   warning: false,  errMsg: 'IndigenousType_VAL_RANGE'};
        break;
      case 'FirstLanguage':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,    targetVal: FirstLanguage,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],   targetVal: FirstLanguage,  bypassNull: true,   warning: false,  errMsg: 'FirstLanguage_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,2],   targetVal: FirstLanguage,  bypassNull: true,   warning: false,  errMsg: 'FirstLanguage_VAL_RANGE'};
        break;
      case 'EnrolledOntario':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,    targetVal: EnrolledOntario,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],   targetVal: EnrolledOntario,  bypassNull: true,   warning: false,  errMsg: 'EnrolledOntario_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],   targetVal: EnrolledOntario,  bypassNull: true,   warning: false,  errMsg: 'EnrolledOntario_VAL_RANGE'};
        break;
      case 'OutOfProvinceResidence':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,    targetVal: OutOfProvinceResidence,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],   targetVal: OutOfProvinceResidence,  bypassNull: true,   warning: false,  errMsg: 'OutOfProvinceResidence_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],   targetVal: OutOfProvinceResidence,  bypassNull: true,   warning: false,  errMsg: 'OutOfProvinceResidence_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12], targetVal: constraints,      bypassNull: false,  warning: false,  errMsg: 'OutOfProvinceResidence_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_VAL_RANGE,       data: [4,6],   targetVal: StatusInCanada,            bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],   targetVal: OutOfProvinceResidence,  bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'StatusInCanada':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,    targetVal: StatusInCanada,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],   targetVal: StatusInCanada,  bypassNull: true,   warning: false,  errMsg: 'StatusInCanada_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,6],   targetVal: StatusInCanada,  bypassNull: true,   warning: false,  errMsg: 'StatusInCanada_VAL_RANGE'};
        break;
      case 'Refugee':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,    targetVal: Refugee,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],   targetVal: Refugee,  bypassNull: true,   warning: false,  errMsg: 'Refugee_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],   targetVal: Refugee,  bypassNull: true,   warning: false,  errMsg: 'Refugee_VAL_RANGE'};
        break;
      case 'BornOutsideCanada':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,    targetVal: BornOutsideCanada,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],   targetVal: BornOutsideCanada,  bypassNull: true,   warning: false,  errMsg: 'BornOutsideCanada_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,2],   targetVal: BornOutsideCanada,  bypassNull: true,   warning: false,  errMsg: 'BornOutsideCanada_VAL_RANGE'};
        //If the value of status in Canada is '3' (Refugee) and the value of born outside Canada is '#' (Missing/Unknown ) or '2' (No), EQAO will set the value of "BornOutsideCanada" to '1' (Yes) by default.(OSSLT BR Role No.63c)(G9 BR Role No.61c) 
        // Confirm from EQAO(Zaheer) this warning message need to be removed (https://bubo.vretta.com/vea/project-management/vretta-project-notes/eqao/admin/-/issues/2174)
        // constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12], targetVal: constraints,   bypassNull: false,  warning: true,  errMsg: 'BornOutsideCanada_VAL_VALIDATE'};
        // constraints [11] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],   targetVal: Refugee,            bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [12] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],   targetVal: BornOutsideCanada,  bypassNull: false,  warning: false,  errMsg: ''};
        //If the value of status in Canada is '3' (Refugee) and the value of born outside Canada is '#' (Missing/Unknown ) or '2' (No), EQAO will set the value of "BornOutsideCanada" to '1' (Yes) by default.(OSSLT BR Role No.63b)(G9 BR Role No.61b)
        // This Rule is change from warning to error  https://bubo.vretta.com/vea/project-management/eqao-shared-access/eqao-data-discrepancies/-/issues/4036 
        constraints [20] = {isSub: false, type: this.C_IF_THEN,         data: [21,22], targetVal: constraints,        bypassNull: false,  warning: false,  errMsg: 'BornOutsideCanada_VAL_VALIDATE_2'};
        constraints [21] = {isSub: true,  type: this.C_VAL_RANGE,       data: [3,3],   targetVal: StatusInCanada,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],   targetVal: BornOutsideCanada,  bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'TimeInCanada':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,    targetVal: TimeInCanada,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],   targetVal: TimeInCanada,       bypassNull: true,   warning: false,  errMsg: 'TimeInCanada_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,5],   targetVal: TimeInCanada,       bypassNull: true,   warning: false,  errMsg: 'TimeInCanada_VAL_RANGE'};
        //constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12], targetVal: constraints,   bypassNull: false,  warning: false,  errMsg: 'TimeInCanada_VAL_VALIDATE'};
        //constraints [11] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],   targetVal: BornOutsideCanada,  bypassNull: false,  warning: false,  errMsg: ''};
        //constraints [12] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,5],   targetVal: TimeInCanada,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [20] = {isSub: false, type: this.C_IF_THEN,         data: [21,22], targetVal: constraints,   bypassNull: false,  warning: false,  errMsg: 'TimeInCanada_VAL_VALIDATE'};
        constraints [21] = {isSub: true,  type: this.C_VAL_RANGE,       data: [2,2],   targetVal: BornOutsideCanada,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,    targetVal: TimeInCanada,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [30] = {isSub: false, type: this.C_IF_THEN,         data: [31,32], targetVal: constraints,   bypassNull: false,  warning: false,  errMsg: 'TimeInCanada_VAL_VALIDATE'};
        constraints [31] = {isSub: true,  type: this.C_IS_NULL,         data: null,    targetVal: BornOutsideCanada,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [32] = {isSub: true,  type: this.C_IS_NULL,         data: null,    targetVal: TimeInCanada,       bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'IEP':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,     targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],    targetVal: IEP,               bypassNull: true,   warning: false,  errMsg: 'IEP_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: true,   warning: false,  errMsg: 'IEP_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,12],    targetVal: IPRCExceptionalities,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        // constraints [11] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [12] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [20] = {isSub: false, type: this.C_IF_THEN,         data: [21,22],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        // constraints [21] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [22] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [30] = {isSub: false, type: this.C_IF_THEN,         data: [31,32],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        // constraints [31] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [32] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [40] = {isSub: false, type: this.C_IF_THEN,         data: [41,42],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        // constraints [41] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [42] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [50] = {isSub: false, type: this.C_IF_THEN,         data: [51,52],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        // constraints [51] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [52] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [60] = {isSub: false, type: this.C_IF_THEN,         data: [61,62],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        // constraints [61] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [62] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [70] = {isSub: false, type: this.C_IF_THEN,         data: [71,72],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        // constraints [71] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [72] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [80] = {isSub: false, type: this.C_IF_THEN,         data: [81,82],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        // constraints [81] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [82] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [90] = {isSub: false, type: this.C_IF_THEN,         data: [91,92],  targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        // constraints [91] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [92] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [100] ={isSub: false, type: this.C_IF_THEN,         data: [101,102],targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        // constraints [101] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [102] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [110] ={isSub: false, type: this.C_IF_THEN,         data: [111,112],targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        // constraints [111] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [112] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [120] ={isSub: false, type: this.C_IF_THEN,         data: [121,122],targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE'};
        // constraints [121] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [122] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [130] ={isSub: false, type: this.C_IF_THEN,         data: [131,132],targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE_2'};
        constraints [131] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: SpecPermIEP,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [132] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [140] ={isSub: false, type: this.C_IF_THEN,         data: [141,142],targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE_3'};
        constraints [141] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: SpecPermTemp,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [142] ={isSub: true,  type: this.C_IS_NULL,         data: null,     targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [150] ={isSub: false, type: this.C_IF_THEN,         data: [151,152],targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE_4'};
        constraints [151] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: SpecPermMoved,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [152] ={isSub: true,  type: this.C_IS_NULL,         data: null,     targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        //Both IEP and a special circumstance cannot be "Yes" for a student. When "LanguageLearnerEarly" is selected, "IEP" can not be selected
        //https://bubo.vretta.com/vea/project-management/vretta-project-notes/vretta-internal-systems/-/issues/685 
        constraints [160] ={isSub: false, type: this.C_IF_THEN,         data: [161,162],targetVal: constraints,          bypassNull: false,  warning: false,  errMsg: 'IEP_VAL_VALIDATE_5'};
        constraints [161] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: LanguageLearnerEarly, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [162] ={isSub: true,  type: this.C_IS_NULL,         data: null,     targetVal: IEP,                  bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'IPRCExceptionalities':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCExceptionalities,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,2],                              targetVal: IPRCExceptionalities,     bypassNull: true,   warning: false,  errMsg: 'IPRCExceptionalities_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,12],                             targetVal: IPRCExceptionalities,     bypassNull: true,   warning: false,  errMsg: 'IPRCExceptionalities_VAL_RANGE'};
        break;  
      // case 'IPRCBehaviour':
      //   constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCBehaviour,     bypassNull: true,   warning: false,  errMsg: 'IPRCBehaviour_CHAR_LEN_RANGE'};
      //   constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCBehaviour,     bypassNull: true,   warning: false,  errMsg: 'IPRCBehaviour_VAL_RANGE'};
      //   constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRC_VAL_VALIDATE'};
      //   constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};
      //   break;
      // case 'IPRCAutism':
      //   constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCAutism,        bypassNull: true,   warning: false,  errMsg: 'IPRCAutism_CHAR_LEN_RANGE'};
      //   constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCAutism,        bypassNull: true,   warning: false,  errMsg: 'IPRCAutism_VAL_RANGE'};
      //   constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRC_VAL_VALIDATE'};
      //   constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};
      //   break;
      // case 'IPRCDeaf':
      //   constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCDeaf,          bypassNull: true,   warning: false,  errMsg: 'IPRCDeaf_CHAR_LEN_RANGE'};
      //   constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCDeaf,          bypassNull: true,   warning: false,  errMsg: 'IPRCDeaf_VAL_RANGE'};
      //   constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRC_VAL_VALIDATE'};
      //   constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};
      //   break;
      // case 'IPRCBlind':
      //   constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCBlind,         bypassNull: true,   warning: false,  errMsg: 'IPRCBlind_CHAR_LEN_RANGE'};
      //   constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCBlind,         bypassNull: true,   warning: false,  errMsg: 'IPRCBlind_VAL_RANGE'};
      //   constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRC_VAL_VALIDATE'};
      //   constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};
      //   break;
      // case 'IPRCGifted':
      //   constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCGifted,        bypassNull: true,   warning: false,  errMsg: 'IPRCGifted_CHAR_LEN_RANGE'};
      //   constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCGifted,        bypassNull: true,   warning: false,  errMsg: 'IPRCGifted_VAL_RANGE'};
      //   constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRC_VAL_VALIDATE'};
      //   constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};
      //   break;
      // case 'IPRCIntellectual':
      //   constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCIntellectual,  bypassNull: true,   warning: false,  errMsg: 'IPRCIntellectual_CHAR_LEN_RANGE'};
      //   constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCIntellectual,  bypassNull: true,   warning: false,  errMsg: 'IPRCIntellectual_VAL_RANGE'};
      //   constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRC_VAL_VALIDATE'};
      //   constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};
      //   break;
      // case 'IPRCDevelopmental':
      //   constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCDevelopmental, bypassNull: true,   warning: false,  errMsg: 'IPRCDevelopmental_CHAR_LEN_RANGE'};
      //   constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCDevelopmental, bypassNull: true,   warning: false,  errMsg: 'IPRCDevelopmental_VAL_RANGE'};
      //   constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRC_VAL_VALIDATE'};
      //   constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};
      //   break;
      // case 'IPRCMultiple':
      //   constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCMultiple,      bypassNull: true,   warning: false,  errMsg: 'IPRCMultiple_CHAR_LEN_RANGE'};
      //   constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCMultiple,      bypassNull: true,   warning: false,  errMsg: 'IPRCMultiple_VAL_RANGE'};
      //   constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRC_VAL_VALIDATE'};
      //   constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};
      //   break;
      // case 'IPRCPhysical':
      //   constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCPhysical,      bypassNull: true,   warning: false,  errMsg: 'IPRCPhysical_CHAR_LEN_RANGE'};
      //   constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCPhysical,      bypassNull: true,   warning: false,  errMsg: 'IPRCPhysical_VAL_RANGE'};
      //   constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRC_VAL_VALIDATE'};
      //   constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};
      //   break;
      // case 'IPRCSpeech':
      //   constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCSpeech,        bypassNull: true,   warning: false,  errMsg: 'IPRCSpeech_CHAR_LEN_RANGE'};
      //   constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCSpeech,        bypassNull: true,   warning: false,  errMsg: 'IPRCSpeech_VAL_RANGE'};
      //   constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRC_VAL_VALIDATE'};
      //   constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};
      //   break;
      // case 'IPRCLanguage':
      //   constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCLanguage,      bypassNull: true,   warning: false,  errMsg: 'IPRCLanguage_CHAR_LEN_RANGE'};
      //   constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCLanguage,      bypassNull: true,   warning: false,  errMsg: 'IPRCLanguage_VAL_RANGE'};
      //   constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRC_VAL_VALIDATE'};
      //   constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};
      //   break;
      // case 'IPRCLearning':
      //   constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                              targetVal: IPRCLearning,      bypassNull: true,   warning: false,  errMsg: 'IPRCLearning_CHAR_LEN_RANGE'};
      //   constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                              targetVal: IPRCLearning,      bypassNull: true,   warning: false,  errMsg: 'IPRCLearning_VAL_RANGE'};
      //   constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                            targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'IPRC_VAL_VALIDATE'};
      //   constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                               targetVal: IPRCLearning,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [12] = {isSub: true,  type: this.C_AND,             data: [13,14,15,16,17,18,19,20,21,22,23], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [13] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCAutism,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDeaf,          bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBlind,         bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCGifted,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCIntellectual,  bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCDevelopmental, bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCMultiple,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCPhysical,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCSpeech,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCLanguage,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,                               targetVal: IPRCBehaviour,     bypassNull: false,  warning: false,  errMsg: ''};
      //   break;
      case 'AccAssistiveTech':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccAssistiveTech,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccAssistiveTech,  bypassNull: true,   warning: false,  errMsg: 'AccAssistiveTech_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,2],         targetVal: AccAssistiveTech,  bypassNull: true,   warning: false,  errMsg: 'AccAssistiveTech_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: 'AccAssistiveTech_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccAssistiveTech,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,     bypassNull: false,  warning: false,  errMsg: ''};
        //When sdc_acc_assistive_tech = 2, the remaining assistive_tech variables should have # only. (sdc_acc_assistive_tech_chrome can be '1' or '#'):   (Export data check #5)
        constraints [20] = {isSub: false, type: this.C_IF_THEN,         data: [21,22],                      targetVal: constraints,                bypassNull: false,  warning: false,  errMsg: 'AccAssistiveTech_VAL_VALIDATE2'};
        constraints [21] = {isSub: true,  type: this.C_VAL_RANGE,       data: [2,2],                        targetVal: AccAssistiveTech,           bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_AND,             data: [23,26,27,28,29,30,31,32,33], targetVal: constraints,                bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_OR,              data: [24,25],                      targetVal: constraints,                bypassNull: false,  warning: false,  errMsg: ''};
        constraints [24] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Chrome,    bypassNull: false,  warning: false,  errMsg: ''};
        constraints [25] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],                        targetVal: AccAssistiveTech_Chrome,    bypassNull: false,  warning: false,  errMsg: ''};
        constraints [26] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Kurz_dl,   bypassNull: false,  warning: false,  errMsg: ''};
        constraints [27] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Kurz_ext,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [28] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Nvda,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [29] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Voiceover, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [30] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Readaloud, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [31] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Jaws,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [32] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Chromevox, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [33] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Natread,   bypassNull: false,  warning: false,  errMsg: ''};
        //The sum of the remaining assistive_tech variables should be 1 if sdc_acc_assistive_tech in ('1','2').  (Export data check #6)(No need to implement an explicit red flag on the school admin side for this rule. https://bubo.vretta.com/vea/project-management/eqao-shared-access/eqao-data-discrepancies/-/issues/3856#note_511558)
        // constraints [40] = {isSub: false, type: this.C_IF_THEN,         data: [41,42],                          targetVal: constraints,                bypassNull: false,  warning: false,  errMsg: 'AccAssistiveTech_VAL_VALIDATE3'};
        // constraints [41] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,2],                            targetVal: AccAssistiveTech,           bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [42] = {isSub: true,  type: this.C_EQUAL_2,         data: [43,1],                           targetVal: constraints,                bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [43] = {isSub: true,  type: this.C_SUM,             data: [44,45,46,47,48,49,50,51,52,53],  targetVal: constraints,                bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [44] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Chrome,    bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [45] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Kurz_dl,   bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [46] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Kurz_ext,  bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [47] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Nvda,      bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [48] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Voiceover, bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [49] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Readaloud, bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [50] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Jaws,      bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [51] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Chromevox, bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [52] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Natread,   bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [53] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Custom,   bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'AccAssistiveTech_PJ':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccAssistiveTech,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccAssistiveTech,  bypassNull: true,   warning: false,  errMsg: 'AccAssistiveTech_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,2],         targetVal: AccAssistiveTech,  bypassNull: true,   warning: false,  errMsg: 'AccAssistiveTech_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: 'AccAssistiveTech_VAL_VALIDATE_PJ'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccAssistiveTech,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16,17], targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: LanguageLearnerEarly,      bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: ESLELD,      bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [18] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: ALFPANA,     bypassNull: false,  warning: false,  errMsg: ''};
        //When sdc_acc_assistive_tech = 2, the remaining assistive_tech variables should have # only. (sdc_acc_assistive_tech_chrome can be '1' or '#'):  (Export data check #5)
        constraints [20] = {isSub: false, type: this.C_IF_THEN,         data: [21,22],                      targetVal: constraints,                bypassNull: false,  warning: false,  errMsg: 'AccAssistiveTech_VAL_VALIDATE2'};
        constraints [21] = {isSub: true,  type: this.C_VAL_RANGE,       data: [2,2],                        targetVal: AccAssistiveTech,           bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_AND,             data: [23,26,27,28,29,30,31,32,33], targetVal: constraints,                bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_OR,              data: [24,25],                      targetVal: constraints,                bypassNull: false,  warning: false,  errMsg: ''};
        constraints [24] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Chrome,    bypassNull: false,  warning: false,  errMsg: ''};
        constraints [25] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],                        targetVal: AccAssistiveTech_Chrome,    bypassNull: false,  warning: false,  errMsg: ''};
        constraints [26] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Kurz_dl,   bypassNull: false,  warning: false,  errMsg: ''};
        constraints [27] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Kurz_ext,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [28] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Nvda,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [29] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Voiceover, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [30] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Readaloud, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [31] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Jaws,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [32] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Chromevox, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [33] = {isSub: true,  type: this.C_IS_NULL,         data: null,                         targetVal: AccAssistiveTech_Natread,   bypassNull: false,  warning: false,  errMsg: ''};
         //The sum of the remaining assistive_tech variables should be 1 if sdc_acc_assistive_tech in ('1','2').  (Export data check #6)(No need to implement an explicit red flag on the school admin side for this rule. https://bubo.vretta.com/vea/project-management/eqao-shared-access/eqao-data-discrepancies/-/issues/3856#note_511558)
        //  constraints [40] = {isSub: false, type: this.C_IF_THEN,         data: [41,42],                          targetVal: constraints,                bypassNull: false,  warning: false,  errMsg: 'AccAssistiveTech_VAL_VALIDATE3'};
        //  constraints [41] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,2],                            targetVal: AccAssistiveTech,           bypassNull: false,  warning: false,  errMsg: ''};
        //  constraints [42] = {isSub: true,  type: this.C_EQUAL_2,         data: [43,1],                           targetVal: constraints,                bypassNull: false,  warning: false,  errMsg: ''};
        //  constraints [43] = {isSub: true,  type: this.C_SUM,             data: [44,45,46,47,48,49,50,51,52,53],  targetVal: constraints,                bypassNull: false,  warning: false,  errMsg: ''};
        //  constraints [44] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Chrome,    bypassNull: false,  warning: false,  errMsg: ''};
        //  constraints [45] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Kurz_dl,   bypassNull: false,  warning: false,  errMsg: ''};
        //  constraints [46] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Kurz_ext,  bypassNull: false,  warning: false,  errMsg: ''};
        //  constraints [47] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Nvda,      bypassNull: false,  warning: false,  errMsg: ''};
        //  constraints [48] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Voiceover, bypassNull: false,  warning: false,  errMsg: ''};
        //  constraints [49] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Readaloud, bypassNull: false,  warning: false,  errMsg: ''};
        //  constraints [50] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Jaws,      bypassNull: false,  warning: false,  errMsg: ''};
        //  constraints [51] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Chromevox, bypassNull: false,  warning: false,  errMsg: ''};
        //  constraints [52] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Natread,   bypassNull: false,  warning: false,  errMsg: ''};
        //  constraints [53] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                             targetVal: AccAssistiveTech_Custom,   bypassNull: false,  warning: false,  errMsg: ''};
        break;
      // case 'AccBraille': // 2024-2025 AccBraille fields are removed from all grades
      //   constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccBraille,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccBraille,        bypassNull: true,   warning: false,  errMsg: 'AccBraille_CHAR_LEN_RANGE'};
      //   constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,4],         targetVal: AccBraille,        bypassNull: true,   warning: false,  errMsg: 'AccBraille_VAL_RANGE'};
      //   constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: 'AccBraille_VAL_VALIDATE'};
      //   constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccBraille,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,       bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,     bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [20] = {isSub: false, type: this.C_IF_THEN,         data: [21,22],       targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: 'AccBraille_VAL_RANGE_2'};
      //   constraints [21] = {isSub: true,  type: this.C_VAL_RANGE,       data: [3,4],         targetVal: AccBraille,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [22] = {isSub: true,  type: this.C_LANG_IS_ENG,     data: null,          targetVal: Language,          bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [30] = {isSub: false, type: this.C_IF_THEN,         data: [31,32],       targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: 'AccBraille_VAL_RANGE_2'};
      //   constraints [31] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,2],         targetVal: AccBraille,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [32] = {isSub: true,  type: this.C_LANG_IS_FRN,     data: null,          targetVal: Language,          bypassNull: false,  warning: false,  errMsg: ''};
      //   break;
      // case 'AccBraille_PJ':
      //   constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccBraille,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccBraille,        bypassNull: true,   warning: false,  errMsg: 'AccBraille_CHAR_LEN_RANGE'};
      //   constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,4],         targetVal: AccBraille,        bypassNull: true,   warning: false,  errMsg: 'AccBraille_VAL_RANGE'};
      //   constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: 'AccBraille_VAL_VALIDATE_PJ'};
      //   constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccBraille,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16,17], targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,       bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,      bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,     bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: LanguageLearnerEarly,      bypassNull: false,  warning: false,  errMsg: ''};
      //   // constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: ESLELD,      bypassNull: false,  warning: false,  errMsg: ''};
      //   // constraints [18] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: ALFPANA,     bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [20] = {isSub: false, type: this.C_IF_THEN,         data: [21,22],       targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: 'AccBraille_VAL_RANGE_2'};
      //   constraints [21] = {isSub: true,  type: this.C_VAL_RANGE,       data: [3,4],         targetVal: AccBraille,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [22] = {isSub: true,  type: this.C_LANG_IS_ENG,     data: null,          targetVal: Language,          bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [30] = {isSub: false, type: this.C_IF_THEN,         data: [31,32],       targetVal: constraints,       bypassNull: false,  warning: false,  errMsg: 'AccBraille_VAL_RANGE_2'};
      //   constraints [31] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,2],         targetVal: AccBraille,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [32] = {isSub: true,  type: this.C_LANG_IS_FRN,     data: null,          targetVal: Language,          bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [40]  = {isSub: false, type: this.C_IF_THEN,         data: [41,42],       targetVal: constraints,        bypassNull: false,  warning: false,  errMsg: 'AccBraille_VAL_VALIDATE_2'};
      //   constraints [41]  = {isSub: true, type: this.C_VAL_RANGE,        data: [1,4],         targetVal: AccBraille,         bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [42]  = {isSub: true, type: this.C_VAL_RANGE,        data: [11,11],       targetVal: IPRCExceptionalities,          bypassNull: false,  warning: false,  errMsg: ''};
      //   break;
      // case 'AccBraille_2':
      //   constraints [10]  = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,        bypassNull: false,  warning: false,  errMsg: 'AccBraille_VAL_VALIDATE_2'};
      //   constraints [11]  = {isSub: true, type: this.C_VAL_RANGE,        data: [1,4],         targetVal: AccBraille,         bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [12]  = {isSub: true, type: this.C_VAL_RANGE,        data: [1,1],         targetVal: IPRCBlind,          bypassNull: false,  warning: false,  errMsg: ''};
      //   break;
      case 'AccAudioVersion':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccAudioVersion,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccAudioVersion,  bypassNull: true,   warning: false,  errMsg: 'AccAudioVersion_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,2],         targetVal: AccAudioVersion,  bypassNull: true,   warning: false,  errMsg: 'AccAudioVersion_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints, bypassNull: false,  warning: false,  errMsg: 'AccAudioVersion_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccAudioVersion,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,    bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'AccAudioVersion_PJ':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccAudioVersion,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccAudioVersion,  bypassNull: true,   warning: false,  errMsg: 'AccAudioVersion_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,2],         targetVal: AccAudioVersion,  bypassNull: true,   warning: false,  errMsg: 'AccAudioVersion_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints, bypassNull: false,  warning: false,  errMsg: 'AccAudioVersion_VAL_VALIDATE_PJ'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccAudioVersion,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16,17], targetVal: constraints, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,    bypassNull: false,  warning: false,  errMsg: ''};
        constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: LanguageLearnerEarly,      bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: ESLELD,      bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [18] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: ALFPANA,     bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [20]  = {isSub: false, type: this.C_IF_THEN,         data: [21,22],       targetVal: constraints,        bypassNull: false,  warning: false,  errMsg: 'AccAudioVersion_VAL_VALIDATE_2'};
        // constraints [21]  = {isSub: true, type: this.C_VAL_RANGE,        data: [1,2],         targetVal: AccAudioVersion,    bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [22]  = {isSub: true, type: this.C_VAL_RANGE,        data: [1,1],         targetVal: IPRCBlind,          bypassNull: false,  warning: false,  errMsg: ''};
        break;
      // case 'AccAudioVersion_2':
      //   constraints [10]  = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,        bypassNull: false,  warning: false,  errMsg: 'AccAudioVersion_VAL_VALIDATE_2'};
      //   constraints [11]  = {isSub: true, type: this.C_VAL_RANGE,        data: [1,2],         targetVal: AccAudioVersion,    bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [12]  = {isSub: true, type: this.C_VAL_RANGE,        data: [1,1],         targetVal: IPRCBlind,          bypassNull: false,  warning: false,  errMsg: ''};
      //   break;
      case 'AccBreaks':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccBreaks,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccBreaks,        bypassNull: true,   warning: false,  errMsg: 'AccBreaks_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],         targetVal: AccBreaks,        bypassNull: true,   warning: false,  errMsg: 'AccBreaks_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,      bypassNull: false,  warning: false,  errMsg: 'AccBreaks_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccBreaks,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,    bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'AccSign':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccSign,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccSign,          bypassNull: true,   warning: false,  errMsg: 'AccSign_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],         targetVal: AccSign,          bypassNull: true,   warning: false,  errMsg: 'AccSign_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,      bypassNull: false,  warning: false,  errMsg: 'AccSign_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccSign,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,    bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'AccSign_PJ':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccSign,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccSign,          bypassNull: true,   warning: false,  errMsg: 'AccSign_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],         targetVal: AccSign,          bypassNull: true,   warning: false,  errMsg: 'AccSign_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,      bypassNull: false,  warning: false,  errMsg: 'AccSign_VAL_VALIDATE_PJ'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccSign,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16,17], targetVal: constraints,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,    bypassNull: false,  warning: false,  errMsg: ''};
        constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: LanguageLearnerEarly,      bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: ESLELD,      bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [18] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: ALFPANA,     bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'AccAudioResponse':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccAudioResponse, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccAudioResponse, bypassNull: true,   warning: false,  errMsg: 'AccAudioResponse_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],         targetVal: AccAudioResponse, bypassNull: true,   warning: false,  errMsg: 'AccAudioResponse_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,      bypassNull: false,  warning: false,  errMsg: 'AccAudioResponse_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccAudioResponse, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,    bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'AccVideotapeResponse':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccVideotapeResponse, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccVideotapeResponse, bypassNull: true,   warning: false,  errMsg: 'AccVideotapeResponse_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],         targetVal: AccVideotapeResponse, bypassNull: true,   warning: false,  errMsg: 'AccVideotapeResponse_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: 'AccVideotapeResponse_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccVideotapeResponse, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,                  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,        bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'AccScribing':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccScribing,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccScribing,          bypassNull: true,   warning: false,  errMsg: 'AccScribing_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],         targetVal: AccScribing,          bypassNull: true,   warning: false,  errMsg: 'AccScribing_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: 'AccScribing_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccScribing,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,                  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,        bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'AccScribing_PJ':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccScribing,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccScribing,          bypassNull: true,   warning: false,  errMsg: 'AccScribing_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],         targetVal: AccScribing,          bypassNull: true,   warning: false,  errMsg: 'AccScribing_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: 'AccScribing_VAL_VALIDATE_PJ'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccScribing,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16,17], targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,                  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: LanguageLearnerEarly,      bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: ESLELD,      bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [18] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: ALFPANA,     bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'AccAltVersion':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccAltVersion,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccAltVersion,          bypassNull: true,   warning: false,  errMsg: 'AccAltVersion_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],         targetVal: AccAltVersion,          bypassNull: true,   warning: false,  errMsg: 'AccAltVersion_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: 'AccAltVersion_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccAltVersion,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,                  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,        bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'AccAltVersion_PJ':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccAltVersion,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccAltVersion,          bypassNull: true,   warning: false,  errMsg: 'AccAltVersion_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],         targetVal: AccAltVersion,          bypassNull: true,   warning: false,  errMsg: 'AccAltVersion_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: 'AccAltVersion_VAL_VALIDATE_PJ'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccAltVersion,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16,17], targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,                  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: LanguageLearnerEarly,      bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'AccOther':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,          targetVal: AccOther,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],         targetVal: AccOther,          bypassNull: true,   warning: false,  errMsg: 'AccOther_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],         targetVal: AccOther,          bypassNull: true,   warning: false,  errMsg: 'AccOther_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],       targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'AccOther_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: AccOther,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14,15,16], targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: IEP,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermIEP,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermTemp,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,          targetVal: SpecPermMoved,     bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'Exemption':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                      targetVal: Exemption,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                     targetVal: Exemption,          bypassNull: true,   warning: false,  errMsg: 'Exemption_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                     targetVal: Exemption,          bypassNull: true,   warning: false,  errMsg: 'Exemption_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                   targetVal: constraints,        bypassNull: false,  warning: false,  errMsg: 'Exemption_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: Exemption,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_AND,             data: [14,15,16,17],       targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [13] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccBraille,           bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccSign,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccScribing,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccAudioVersion,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [17] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccAssistiveTech,     bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'Exemptions':
        constraints [10] = {isSub: false, type: this.C_IF_THEN,        data: [11,12],                   targetVal: constraints,        bypassNull: false,  warning: false,  errMsg: 'Exemption_VAL_VALIDATE_2'};
        constraints [11] = {isSub: true,  type: this.C_AND,            data: [13,14,15],                targetVal: constraints,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_AND,            data: [16,17,18],                   targetVal: constraints,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_NOT_NULL,       data: null,                      targetVal: ExemptionRead,           bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,       data: null,                      targetVal: ExemptionWrite,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,       data: null,                      targetVal: ExemptionMath,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: SpecPermTemp,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [17] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: SpecPermMoved,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [18] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: LanguageLearnerEarly,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [20] = {isSub: false, type: this.C_IF_THEN,        data: [21,22],                   targetVal: constraints,        bypassNull: false,  warning: false,  errMsg: 'Exemption_VAL_VALIDATE_3'};
        constraints [21] = {isSub: true,  type: this.C_AND,            data: [23,24,25],                targetVal: constraints,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_AND,            data: [27,28,29,30,31,32,33,34,35,36,37,41,42,43,44,45,46,47,48,49,50], targetVal: constraints,   bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_NOT_NULL,       data: null,                      targetVal: ExemptionRead,           bypassNull: false,  warning: false,  errMsg: ''};
        constraints [24] = {isSub: true,  type: this.C_NOT_NULL,       data: null,                      targetVal: ExemptionWrite,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [25] = {isSub: true,  type: this.C_NOT_NULL,       data: null,                      targetVal: ExemptionMath,          bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [26] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccBraille,           bypassNull: false,  warning: false,  errMsg: ''};
        constraints [27] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccBreaks,            bypassNull: false,  warning: false,  errMsg: ''};
        constraints [28] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccSign,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [29] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccAudioResponse,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [30] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccVideotapeResponse, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [31] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccScribing,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [32] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccOther,             bypassNull: false,  warning: false,  errMsg: ''};
        constraints [33] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccAudioVersion,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [34] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccAssistiveTech,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [35] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccAssistiveTechRead, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [36] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccAssistiveTechWrite,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [37] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccAssistiveTechMath,   bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [38] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccBrailleRead,         bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [39] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccBrailleWrite,        bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [40] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccBrailleMath,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [41] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccAudioVersionRead,    bypassNull: false,  warning: false,  errMsg: ''};
        constraints [42] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccAudioVersionWrite,   bypassNull: false,  warning: false,  errMsg: ''};
        constraints [43] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccAudioVersionMath,    bypassNull: false,  warning: false,  errMsg: ''};
        constraints [44] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccSignRead,            bypassNull: false,  warning: false,  errMsg: ''};
        constraints [45] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccSignWrite,           bypassNull: false,  warning: false,  errMsg: ''};
        constraints [46] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccSignMath,            bypassNull: false,  warning: false,  errMsg: ''};
        constraints [47] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccScribingRead,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [48] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccScribingWrite,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [49] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccScribingMath,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [50] = {isSub: true,  type: this.C_IS_NULL,        data: null,                      targetVal: AccAltVersion,          bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'SpecEdNoExpectationReadWrite':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                      targetVal: SpecEdNoExpectationReadWrite,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                     targetVal: SpecEdNoExpectationReadWrite,          bypassNull: true,   warning: false,  errMsg: 'SpecEdNoExpectationReadWrite_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                     targetVal: SpecEdNoExpectationReadWrite,          bypassNull: true,   warning: false,  errMsg: 'SpecEdNoExpectationReadWrite_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                   targetVal: constraints,                           bypassNull: false,  warning: false,  errMsg: 'SpecEdNoExpectationReadWrite_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],                      targetVal: SpecEdNoExpectationReadWrite,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],                     targetVal: IEP,                                   bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'SpecEdNoExpectationMath':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                      targetVal: SpecEdNoExpectationMath,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                     targetVal: SpecEdNoExpectationMath,          bypassNull: true,   warning: false,  errMsg: 'SpecEdNoExpectationMath_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                     targetVal: SpecEdNoExpectationMath,          bypassNull: true,   warning: false,  errMsg: 'SpecEdNoExpectationMath_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                   targetVal: constraints,                           bypassNull: false,  warning: false,  errMsg: 'SpecEdNoExpectationMath_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],                      targetVal: SpecEdNoExpectationMath,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],                     targetVal: IEP,                                   bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'SpecPermIEP':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                      targetVal: SpecPermIEP,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                     targetVal: SpecPermIEP,          bypassNull: true,   warning: false,  errMsg: 'SpecPermIEP_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                     targetVal: SpecPermIEP,          bypassNull: true,   warning: false,  errMsg: 'SpecPermIEP_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                   targetVal: constraints,          bypassNull: false,  warning: false,  errMsg: 'SpecPermIEP_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: SpecPermIEP,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [15,16,17,18,19,20,21,22,23,24,25],  targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        // AccBraille and AccBreaks removed from OSSLT/G9 bussiness validation rule 2024-2025
        // constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBraille,           bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBreaks,            bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccSign,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioResponse,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccVideotapeResponse, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [18] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccScribing,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [19] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccOther,             bypassNull: false,  warning: false,  errMsg: ''};
        constraints [20] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioVersion,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [21] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTech,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechRead, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechWrite,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [24] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechMath, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [25] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAltVersion, bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'SpecPermTemp':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                      targetVal: SpecPermTemp,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                     targetVal: SpecPermTemp,         bypassNull: true,   warning: false,  errMsg: 'SpecPermTemp_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                     targetVal: SpecPermTemp,         bypassNull: true,   warning: false,  errMsg: 'SpecPermTemp_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                   targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: 'SpecPermTemp_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: SpecPermTemp,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [15,16,17,18,19,20,21,22,23,24,28,29,30,31,32,33,34,35,36,37], targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        // AccBraille and AccBreaks removed from OSSLT/G9 bussiness validation rule 2024-2025
        // constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBraille,           bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBreaks,            bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccSign,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioResponse,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccVideotapeResponse, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [18] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccScribing,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [19] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccOther,             bypassNull: false,  warning: false,  errMsg: ''};
        constraints [20] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioVersion,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [21] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTech,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechRead, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechWrite,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [24] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechMath,   bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [25] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBrailleRead,         bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [26] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBrailleWrite,        bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [27] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBrailleMath,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [28] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioVersionRead,    bypassNull: false,  warning: false,  errMsg: ''};
        constraints [29] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioVersionWrite,   bypassNull: false,  warning: false,  errMsg: ''};
        constraints [30] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioVersionMath,    bypassNull: false,  warning: false,  errMsg: ''};
        constraints [31] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccSignRead,            bypassNull: false,  warning: false,  errMsg: ''};
        constraints [32] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccSignWrite,           bypassNull: false,  warning: false,  errMsg: ''};
        constraints [33] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccSignMath,            bypassNull: false,  warning: false,  errMsg: ''};
        constraints [34] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccScribingRead,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [35] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccScribingWrite,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [36] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccScribingMath,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [37] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAltVersion,        bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'SpecPermMoved':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                      targetVal: SpecPermMoved,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                     targetVal: SpecPermMoved,        bypassNull: true,   warning: false,  errMsg: 'SpecPermMoved_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                     targetVal: SpecPermMoved,        bypassNull: true,   warning: false,  errMsg: 'SpecPermMoved_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                   targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: 'SpecPermMoved_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: SpecPermMoved,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [15,16,17,18,19,20,21,22,23,24,28,29,30,31,32,33,34,35,36,37], targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        // AccBraille and AccBreaks removed from OSSLT/G9 bussiness validation rule 2024-2025
        // constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBraille,           bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBreaks,            bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccSign,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioResponse,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccVideotapeResponse, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [18] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccScribing,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [19] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccOther,             bypassNull: false,  warning: false,  errMsg: ''};
        constraints [20] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioVersion,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [21] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTech,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechRead, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechWrite,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [24] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechMath, bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [25] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBrailleRead,         bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [26] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBrailleWrite,        bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [27] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBrailleMath,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [28] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioVersionRead,    bypassNull: false,  warning: false,  errMsg: ''};
        constraints [29] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioVersionWrite,   bypassNull: false,  warning: false,  errMsg: ''};
        constraints [30] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioVersionMath,    bypassNull: false,  warning: false,  errMsg: ''};
        constraints [31] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccSignRead,            bypassNull: false,  warning: false,  errMsg: ''};
        constraints [32] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccSignWrite,           bypassNull: false,  warning: false,  errMsg: ''};
        constraints [33] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccSignMath,            bypassNull: false,  warning: false,  errMsg: ''};
        constraints [34] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccScribingRead,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [35] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccScribingWrite,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [36] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccScribingMath,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [37] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAltVersion,        bypassNull: false,  warning: false,  errMsg: ''};
        break;
      // case 'ESLELD':
      //   constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                   targetVal: ESLELD,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                  targetVal: ESLELD,        bypassNull: true,   warning: false,  errMsg: 'ESLELD_CHAR_LEN_RANGE'};
      //   constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                  targetVal: ESLELD,        bypassNull: true,   warning: false,  errMsg: 'ESLELD_VAL_RANGE'};
      //   break;
      // case 'ALFPANA':
      //   constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                   targetVal: ALFPANA,        bypassNull: false,  warning: false,  errMsg: ''};
      //   constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                  targetVal: ALFPANA,        bypassNull: true,   warning: false,  errMsg: 'ALFPANA_CHAR_LEN_RANGE'};
      //   constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,2],                  targetVal: ALFPANA,        bypassNull: true,   warning: false,  errMsg: 'ALFPANA_VAL_RANGE'};
      //   break;
      case 'LanguageLearner':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                   targetVal: LanguageLearner,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                  targetVal: LanguageLearner,        bypassNull: true,   warning: false,  errMsg: 'LanguageLearner_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,3],                  targetVal: LanguageLearner,        bypassNull: true,   warning: false,  errMsg: 'LanguageLearner_VAL_RANGE'};
        constraints [10] ={isSub: false, type: this.C_IF_THEN,         data: [11,12],targetVal: constraints,  bypassNull: false,  warning: false,  errMsg: 'ELL_VAL_VALIDATE_1'};
        constraints [11] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: LanguageLearnerEarly,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] ={isSub: true,  type: this.C_VAL_RANGE,       data: [1,3],    targetVal: LanguageLearner,               bypassNull: false,  warning: false,  errMsg: ''};
        //Only students who are attending French schools should have values in'#', '2' and '3' for sdc_Language_Learner.  (Export data check #8)
        constraints [20] = {isSub: false, type: this.C_IF_THEN,         data: [21,22],  targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: 'LanguageLearner_VAL_VALIDATE_2'};
        constraints [21] = {isSub: true,  type: this.C_LANG_IS_FRN,     data: null,     targetVal: Language,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_OR,              data: [23,24],  targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_IS_NULL,         data: null,     targetVal: LanguageLearner, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [24] = {isSub: true,  type: this.C_VAL_RANGE,       data: [2,3],    targetVal: LanguageLearner, bypassNull: false,  warning: false,  errMsg: ''};
        //English students should only have '#' or 1 for sdc_Language_Learner.  (Export data check #8)
        constraints [30] = {isSub: false, type: this.C_IF_THEN,         data: [31,32],  targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: 'LanguageLearner_VAL_VALIDATE_3'};
        constraints [31] = {isSub: true,  type: this.C_LANG_IS_ENG,     data: null,     targetVal: Language,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [32] = {isSub: true,  type: this.C_OR,              data: [33,34],  targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [33] = {isSub: true,  type: this.C_IS_NULL,         data: null,     targetVal: LanguageLearner, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [34] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],    targetVal: LanguageLearner, bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'LanguageLearnerEarly':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                   targetVal: LanguageLearnerEarly,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                  targetVal: LanguageLearnerEarly,        bypassNull: true,   warning: false,  errMsg: 'LanguageLearnerEarly_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                  targetVal: LanguageLearnerEarly,        bypassNull: true,   warning: false,  errMsg: 'LanguageLearnerEarly_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                   targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: 'LanguageLearnerEarly_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: LanguageLearnerEarly,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_OR,              data: [14,15,16,17,18,19,20,21,22,23,24,28,29,30,31,32,33,34,35,36,37], targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBraille,           bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBreaks,            bypassNull: false,  warning: false,  errMsg: ''};
        constraints [15] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccSign,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [16] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioResponse,     bypassNull: false,  warning: false,  errMsg: ''};
        constraints [17] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccVideotapeResponse, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [18] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccScribing,          bypassNull: false,  warning: false,  errMsg: ''};
        constraints [19] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccOther,             bypassNull: false,  warning: false,  errMsg: ''};
        constraints [20] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioVersion,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [21] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTech,      bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechRead, bypassNull: false,  warning: false,  errMsg: ''};
        constraints [23] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechWrite,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [24] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAssistiveTechMath,   bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [25] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBrailleRead,         bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [26] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBrailleWrite,        bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [27] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccBrailleMath,         bypassNull: false,  warning: false,  errMsg: ''};
        constraints [28] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioVersionRead,    bypassNull: false,  warning: false,  errMsg: ''};
        constraints [29] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioVersionWrite,   bypassNull: false,  warning: false,  errMsg: ''};
        constraints [30] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAudioVersionMath,    bypassNull: false,  warning: false,  errMsg: ''};
        constraints [31] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccSignRead,            bypassNull: false,  warning: false,  errMsg: ''};
        constraints [32] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccSignWrite,           bypassNull: false,  warning: false,  errMsg: ''};
        constraints [33] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccSignMath,            bypassNull: false,  warning: false,  errMsg: ''};
        constraints [34] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccScribingRead,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [35] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccScribingWrite,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [36] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccScribingMath,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [37] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                      targetVal: AccAltVersion,        bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'SpecProvBreaks':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                   targetVal: SpecProvBreaks,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                  targetVal: SpecProvBreaks,       bypassNull: true,   warning: false,  errMsg: 'SpecProvBreaks_CHAR_LEN_RANGE'};
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,1],                  targetVal: SpecProvBreaks,       bypassNull: true,   warning: false,  errMsg: 'SpecProvBreaks_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [11,12],                targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: 'SpecProvBreaks_VAL_VALIDATE'};
        constraints [11] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                   targetVal: SpecProvBreaks,       bypassNull: false,  warning: false,  errMsg: ''};
        constraints [12] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                   targetVal: LanguageLearner,       bypassNull: false,  warning: false,  errMsg: ''};
        //constraints [12] = {isSub: true,  type: this.C_OR,              data: [13,14],                targetVal: constraints,     bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [13] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                   targetVal: ESLELD,               bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [14] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                   targetVal: ALFPANA,              bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'NonParticipationStatus':
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,                       targetVal: NonParticipationStatus,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [3]  = {isSub: false, type: this.C_CHAR_LEN_RANGE,  data: [1,1],                      targetVal: NonParticipationStatus,  bypassNull: true,   warning: false,  errMsg: 'NonParticipationStatus_CHAR_LEN_RANGE'};
        // Add new value (4) OSSLT SPRING for OSSLT data definition 2024-2025
        constraints [4]  = {isSub: false, type: this.C_VAL_RANGE,       data: [1,4],                      targetVal: NonParticipationStatus,  bypassNull: true,   warning: false,  errMsg: 'NonParticipationStatus_VAL_RANGE'};
        constraints [10] = {isSub: false, type: this.C_IF_THEN,         data: [12,13],                    targetVal: constraints,        bypassNull: false,  warning: true,   errMsg: 'NonParticipationStatus_VAL_VALIDATE'};
        constraints [12] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                       targetVal: NonParticipationStatus,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [13] = {isSub: true,  type: this.C_AND,             data: [14,17,18,19,20,21,22],  targetVal: constraints,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [14] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: AccAssistiveTech,        bypassNull: false,  warning: false,  errMsg: ''};
        // AccBraille and AccBreaks removed from OSSLT bussiness validation rule 2024-2025
        // constraints [15] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: AccBraille,              bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [16] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: AccBreaks,               bypassNull: false,  warning: false,  errMsg: ''};
        constraints [17] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: AccSign,                 bypassNull: false,  warning: false,  errMsg: ''};
        constraints [18] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: AccAudioResponse,        bypassNull: false,  warning: false,  errMsg: ''};
        constraints [19] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: AccVideotapeResponse,    bypassNull: false,  warning: false,  errMsg: ''};
        constraints [20] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: AccScribing,             bypassNull: false,  warning: false,  errMsg: ''};
        constraints [21] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: AccOther,                bypassNull: false,  warning: false,  errMsg: ''};
        constraints [22] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: AccAltVersion,             bypassNull: false,  warning: false,  errMsg: ''};
        constraints [30] = {isSub: false, type: this.C_IF_THEN,         data: [31,32],                    targetVal: constraints,             bypassNull: false,  warning: true,   errMsg: 'NonParticipationStatus_VAL_VALIDATE_2'};
        constraints [31] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                       targetVal: NonParticipationStatus,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [32] = {isSub: true,  type: this.C_AND,             data: [33,34,35,36],              targetVal: constraints,             bypassNull: false,  warning: false,  errMsg: ''};
        constraints [33] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: SpecPermIEP,             bypassNull: false,  warning: false,  errMsg: ''};
        constraints [34] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: SpecPermTemp,            bypassNull: false,  warning: false,  errMsg: ''};
        constraints [35] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: SpecPermMoved,           bypassNull: false,  warning: false,  errMsg: ''};
        constraints [36] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: LanguageLearnerEarly,           bypassNull: false,  warning: false,  errMsg: ''};
        // SpecProvBreaks removed from OSSLT/G9 bussiness validation rule 2024-2025
        // constraints [40] = {isSub: false, type: this.C_IF_THEN,         data: [41,42],                    targetVal: constraints,             bypassNull: false,  warning: true,   errMsg: 'NonParticipationStatus_VAL_VALIDATE_3'};
        // constraints [41] = {isSub: true,  type: this.C_NOT_NULL,        data: null,                       targetVal: NonParticipationStatus,  bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [42] = {isSub: true,  type: this.C_AND,             data: [45],                       targetVal: constraints,             bypassNull: false,  warning: false,  errMsg: ''};
        //constraints [43] = {isSub: true,  type: this.C_IS_NULL,       data: null,                       targetVal: ESLELD,                  bypassNull: false,  warning: false,  errMsg: ''};
        //constraints [44] = {isSub: true,  type: this.C_IS_NULL,       data: null,                       targetVal: ALFPANA,                 bypassNull: false,  warning: false,  errMsg: ''};
        // constraints [45] = {isSub: true,  type: this.C_IS_NULL,         data: null,                       targetVal: SpecProvBreaks,          bypassNull: false,  warning: false,  errMsg: ''};

        constraints [50] = {isSub: false, type: this.C_IF_THEN,         data: [51,52],                    targetVal: constraints,             bypassNull: false,  warning: false,  errMsg: 'NonParticipationStatus_VAL_VALIDATE_4'};
        constraints [51] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],                      targetVal: NonParticipationStatus,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [52] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],                      targetVal: IEP,                     bypassNull: false,  warning: false,  errMsg: ''};
        //If NonParticipationStatus = 1 (exempt) or 2 (deferred) the EligibilityStatus must be 1 (FTE) or 2 (PE).(Export data check #30)
        constraints [60] = {isSub: false, type: this.C_IF_THEN,         data: [61,62],                    targetVal: constraints,             bypassNull: false,  warning: false,  errMsg: 'NonParticipationStatus_VAL_VALIDATE_5'};
        constraints [61] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,2],                      targetVal: NonParticipationStatus,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [62] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,2],                      targetVal: EligibilityStatus,       bypassNull: false,  warning: false,  errMsg: ''};
        //If NonParticipationStatus = 3 (OSSLC) then EligibilityStatus must be 2 (PE).  (Export data check #30)
        // From EQAO request this BR is longer valid https://bubo.vretta.com/vea/project-management/eqao-shared-access/eqao-data-discrepancies/-/issues/4054
        // 2024/10/07 Rule 27 re-applied for year 2024-2025 https://bubo.vretta.com/vea/project-management/eqao-shared-access/uat-sessions/2024-osslt-fall/-/issues/37
        constraints [63] = {isSub: false, type: this.C_IF_THEN,         data: [64,65],                    targetVal: constraints,             bypassNull: false,  warning: false,  errMsg: 'NonParticipationStatus_VAL_VALIDATE_6'};
        constraints [64] = {isSub: true,  type: this.C_VAL_RANGE,       data: [3,3],                      targetVal: NonParticipationStatus,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [65] = {isSub: true,  type: this.C_VAL_RANGE,       data: [2,2],                      targetVal: EligibilityStatus,       bypassNull: false,  warning: false,  errMsg: ''};
        // If "NonParticipationStatus" = 4 (OSSLC - spring FTE), then the "EligibilityStatus" must be 1 (first-time eligible). (Rule #27) (Added on 2024-2025)
        constraints [66] = {isSub: false, type: this.C_IF_THEN,         data: [67,68],                    targetVal: constraints,             bypassNull: false,  warning: false,  errMsg: 'NonParticipationStatus_VAL_VALIDATE_8'};
        constraints [67] = {isSub: true,  type: this.C_VAL_RANGE,       data: [4,4],                      targetVal: NonParticipationStatus,  bypassNull: false,  warning: false,  errMsg: ''};
        constraints [68] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],                      targetVal: EligibilityStatus,       bypassNull: false,  warning: false,  errMsg: ''};
        //If "Graduating" = 1 (Yes), "Non-ParticipationStatus" cannot = 2 (Deferred). If this combination of values are provided, EQAO will assign the value of '#' (No) to the "NonParticipationStatus" field by default. (OSSLT BR Role No.27b)
        constraints [70] = {isSub: false, type: this.C_IF_THEN,         data: [71,72],                    targetVal: constraints,             bypassNull: false,  warning: true,   errMsg: 'NonParticipationStatus_VAL_VALIDATE_7'}
        constraints [71] = {isSub: true,  type: this.C_VAL_RANGE,       data: [1,1],                      targetVal: Graduating,              bypassNull: false,  warning: false,  errMsg: ''};
        constraints [72] = {isSub: true,  type: this.C_NOT_VAL_RANGE,   data: [2,2],                      targetVal: NonParticipationStatus,  bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'FrenchImmersion_G9_OSSLT':
        //All records for OSSLT should have a value of '#' for sdc_french_immersion. (Export data check #7)
        //G9 students should not have any values for sdc_french_immersion. 
        constraints [2]  = {isSub: false, type: this.C_IS_NULL,         data: null,    targetVal: FrenchImmersion,  bypassNull: false,  warning: false,  errMsg: 'FrenchImmersion_NEED_TO_BE_NULL'};
        break;
      case 'FrenchImmersion_PJ':
        //Any records that have a sdc_french_immersion value that is not equal to '#' should be flagged. (Export data check #7)
        //If class already exist Student FrenchImmersion will auto match class is_fi value so no need to check the input.
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,    targetVal: FrenchImmersion,  bypassNull: false,  warning: false,  errMsg: ''};
        break;
      case 'FrenchImmersionOrExtended_OSSLT_PJ':
        //All records for OSSLT should have a value of '#' for sdc_french_immersion_extended and sdc_french_immersion. (Export data check #7)
        constraints [2]  = {isSub: false, type: this.C_IS_NULL,         data: null,    targetVal: FrenchImmersionOrExtended,  bypassNull: false,  warning: false,  errMsg: 'FrenchImmersionOrExtended_NEED_TO_BE_NULL'};
        break;
      case 'FrenchImmersionOrExtended_G9':
        //Any records that have a sdc_french_immersion_extended value that is not equal to '#' should be flagged. (Export data check #7)
        //If class already exist student FrenchImmersionOrExtended will auto match class is_fi value so no need to check the input.
        constraints [2]  = {isSub: false, type: this.C_NULL_OK,         data: null,    targetVal: FrenchImmersionOrExtended,  bypassNull: false,  warning: false,  errMsg: ''};
        break; 
      default:
        break;
    }
    return constraints;
  }

  async executeConstraints(constraints:any,ErrorMessages:any, warningMessages:any){
    for (let index in constraints){
      const indexNum = Number(index);
      const constraint = constraints[indexNum]
      if(!constraint.isSub){ // execute constraint if not sub
        if(!(await this.executeConstraint(constraint))){//constraint check does not pass
          if(constraint.warning){ //add to warning message if its a warning
            const warMsgIndex = warningMessages.indexOf(constraint.errMsg)
            if(warMsgIndex == -1){
              warningMessages.push (constraint.errMsg)
            }
          }else{ //add to error message if its a error
            const errMsgIndex = ErrorMessages.indexOf(constraint.errMsg)
            if(errMsgIndex == -1){
              ErrorMessages.push (constraint.errMsg)
            }
            return; //skip the rest of constraint check. since an error occure already
          }
        }
      }
    }
  }

  async executeConstraint(constraint : Constraint):Promise<any>{
    if(await this.C_IS_NULL(constraint.targetVal, constraint.data) && constraint.bypassNull){ //bypass if null
      return true;
    }
    try{
      return await constraint.type(constraint.targetVal, constraint.data);
    }catch(e){
      return false;
    }
  }

  C_NOT_NULL = async (value:any, data:any ) :Promise<boolean> => {
    return value != null && value.length > 0;
  }

  C_IS_NULL = async (value:any, data:any ) :Promise<boolean> => {
    return value == undefined || value == null || value.length == 0;
  }

  C_NULL_OK = async (value:any, data:any ) :Promise<boolean> => {
    return value == undefined || value != null || value == null;
  }

  C_CHAR_LEN_RANGE = async (value:any, data:any ) :Promise<boolean> => {
    return value.length >= data[0] && value.length <= data[1];
  }

  C_VAL_RANGE = async (value:any, data:any ) :Promise<boolean> => {
    return Number(value) >= data[0] && Number(value) <= data[1];
  }

  /**",
   * check value is not with in data[0]  data[1]
   * @param value input number
   * @param data two number array
   * @returns boolean
   */
  C_NOT_VAL_RANGE = async (value:any, data:any ) :Promise<boolean> => {
    return !(await this.C_VAL_RANGE(value,data))
  }

  C_CHAR_PATTERN = async (value:any, data:any ) :Promise<boolean> => {
    return data.test(value);
  }

  C_OR = async (value:any, data:any ) :Promise<boolean> => {
    for (let i =0; i<data.length;i++){
      const index = data[i];
      const constraint = value[index];
      if(await this.executeConstraint(constraint)){ //constraint pass check
        return true
      }
    }
    return false;
  }

  C_AND = async (value:any, data:any ) :Promise<boolean> => {
    for (let i =0; i<data.length;i++){
      const index = data[i];
      const constraint = value[index];
      if(! (await this.executeConstraint(constraint))){ //constraint does not pass check
        return false;
      }
    }
    return true;
  }

  C_IF_THEN = async (value:any, data:any ) :Promise<boolean> => {
    const firstConstraint = value[data[0]];
    const secondConstraint = value[data[1]];
    if(await this.executeConstraint(firstConstraint) && ! await this.executeConstraint(secondConstraint)){
      return false;
    }
    return true;
  }

  C_EQUAL = async (value:any, data:any ) :Promise<boolean> => {
    return value == data
  }

  C_NOT_EQUAL = async (value:any, data:any ) :Promise<boolean> => {
    return value !== data
  }

  /**",
   * check if (constraint index data[0])'s result eqaul to data[1] 
   * @param value list of constraints
   * @param data two number first is the contraints index and second is the compar value
   * @returns boolean
   */
  C_EQUAL_2 = async (value:any, data:any ) :Promise<boolean> => {
    const index = data[0];
    const constraint = value[index];
    const constraintValue =  await this.executeConstraint(constraint)
    const compareValue = data[1]
    return this.C_EQUAL(constraintValue, compareValue)
  }

    /**",
   * Add the sum in the data list's values 
   * @param value constriant that get the data lsit item's value(number) 
   * @param data list of values(number)
   * @returns number
   */
    C_SUM = async (value:any, data:any ) :Promise<number> => {
      let total = 0
      for (let i =0; i<data.length;i++){
        const index = data[i];
        const constraint = value[index];
        const dataIndexValue =  await this.executeConstraint(constraint)
        total +=  dataIndexValue
      }
      return total   
    }

  C_DATE_VALIDATE = async (value:any, data:any ) :Promise<boolean> => {
    //The date of the dob field must be YYYYMMDD (Export data check #22)
    return moment(value, "YYYYMMDD", true).isValid() && moment(value, "YYYYMMDD", true) < moment()
  }

  C_DATE_DIFF_GREATER = async (value:any, data:any ) :Promise<boolean> => {
    if(value == undefined || value == null || value == ''){
      return true;
    }
    // return Math.floor((Date.now() - moment(value, 'YYYYMMDD').toDate().getTime()) / 3.15576e+10) > data
    let diff = moment().diff(moment(value, 'YYYYMMDD'), 'year', true);
    return moment().diff(moment(value, 'YYYYMMDD'), 'year', true) > data;
  }

  C_IS_SASN_LOGIN = async (value:any, data:any ) :Promise<boolean> => {
    return +value === 1;
  }

  C_DATE_DIFF_SMALLER = async (value:any, data:any ) :Promise<boolean> => {
    if(value == undefined || value == null || value == ''){
      return true;
    }
    // return Math.floor((Date.now() - moment(value, 'YYYYMMDD').toDate().getTime()) / 3.15576e+10) < data
    let diff = moment().diff(moment(value, 'YYYYMMDD'), 'year', true);
    return moment().diff(moment(value, 'YYYYMMDD'), 'year', true) < data;
  }
  C_DATE_SMALLER = async (value:any, data:any ) :Promise<boolean> => {
    if(value == undefined || value == null || value == ''|| data == undefined || data == null || data == ''){
      return true;
    }
    return moment(value, 'YYYYMMDD').toDate().getTime() <= moment(data, 'YYYYMMDD').toDate().getTime()
  }
  C_DATE_GREATER = async (value:any, data:any ) :Promise<boolean> => {
    if(value == undefined || value == null || value == ''|| data == undefined || data == null || data == ''){
      return true;
    }
    return moment(value, 'YYYYMMDD').toDate().getTime() >= moment(data, 'YYYYMMDD').toDate().getTime()
  }

  C_OEN_DIGIT_CHECK = async (value:any, data:any ) :Promise<boolean> => {
    if(value == '000000000'){
      return true;
    }
    var retValue = 10 - (await this.OENMast(Number(value.substring(0,2))+1)+
                           await this.OENMast(Number(value.substring(2,4))+1)+
                           await this.OENMast(Number(value.substring(4,6))+1)+
                           await this.OENMast(Number(value.substring(6,8))+1)) % 10
    if(retValue == 10){
      retValue =0;
    }
    const comparValue = Number(value.substring(8,9));
    if(retValue != comparValue){
      return false;
    }
    return true;
  }

  C_VALIDE_GRPING = async (value:any, data:any ) :Promise<boolean> => {
    const studentID = Number(data);
    var records = await dbRawRead(this.app, [
      value
    ], `
        select id
        from school_classes as sc
        where sc.name = ?
    `)
    if(records.length > 0){
      return true;
    }
    return false;
  }

  C_VALIDE_GRPING_2 = async (value:any, data:any ) :Promise<boolean> => {
    const studentID = Number(data);
    var records = await dbRawRead(this.app, [
      value
    ], `
      select sc.id
      from school_classes sc
      join school_semesters sm
        on sm.id = sc.semester_id
      join test_windows tw
        on tw.id =  sm.test_window_id
      and tw.date_end > now()
      where sc.name = ?
    `)
    if(records.length > 0){
      return true;
    }
    return false;
  }

  C_VALIDE_CLASS = async (value:any, data:any ) :Promise<boolean> => {
    const studentID = Number(data);
    var records = await dbRawRead(this.app, [
      value
    ], `
        select id
        from school_classes as sc
        where sc.name = ?
    `)
    if(records.length > 0 ){
      return true;
    }
    return false;
  }

  C_VALIDE_CLASS_2 = async (value:any, data:any ) :Promise<boolean> => {
    const studentID = Number(data);
    var records = await dbRawRead(this.app, [
      value
    ], `
      select sc.id
        from school_classes sc
        join school_semesters sm
          on sm.id = sc.semester_id
        join test_windows tw
          on tw.id =  sm.test_window_id
         and tw.date_end > now()
       where sc.name = ?
    `)
    if(records.length > 0 ){
      return true;
    }
    return false;
  }

  C_VALIDE_CLASS_ID = async (value:any, data:any ) :Promise<boolean> => {
    if(!value){  //The class does not exist yet 
      return true;  
    }
    var records = await dbRawRead(this.app, [
      value
    ], `
      select sc.id
        from school_classes sc
        join school_semesters sm
          on sm.id = sc.semester_id
        join test_windows tw
          on tw.id =  sm.test_window_id
         and (${QCheckInfoLock}) = false
         and tw.reg_lock_on > now()
       where sc.id = ?
    `)
    if(records.length > 0 ){
      return true;
    }
    return false;
  }

  C_VALIDE_HOMEROOM = async (value:any, data:any ) :Promise<boolean> => {
    //need to implement this
    if(value == 'Invalid HomeRoom Name')
      return false;
    return true;
  }

  C_LANG_IS_ENG = async (value:any, data:any ) :Promise<boolean> => {
    if(value === 'en'){
      return true;
    }
    return false;
  }

  C_LANG_IS_FRN = async (value:any, data:any ) :Promise<boolean> => {
    if(value === 'fr'){
      return true;
    }
    return false;
  }

  C_SASN_UNIQUE = async (value:any, data:any ) :Promise<boolean> => {
    return this.C_OEN_UNIQUE (value, data, true)
  }

  C_OEN_UNIQUE = async (value:any, data:any, isSASN = false ) :Promise<boolean> => {
    //need to update this to each school
    const studentID = Number(data.StudentID);
    const SchGroupID = Number(data.SchGroupID);
    //const Namespace = data.Namespace === 'eqao_g10' ? 'IS_G10':'IS_G9'
    let group_type;
    let Namespace;
    switch(data.Namespace){
      case 'eqao_g3':
        Namespace = 'IS_G3'
        group_type = 'EQAO_G3'
        break;
      case 'eqao_g6':
        Namespace = 'IS_G6'
        group_type = 'EQAO_G6'
        break;
      case 'eqao_g9':
        Namespace = 'IS_G9'
        group_type = 'EQAO_G9'
        break;
      case 'eqao_g10':
        Namespace = 'IS_G10'
        group_type = 'EQAO_G10'
        break;
      default:
        Namespace = 'IS_G3'
        group_type = 'EQAO_G3'
        break;
    }

    var records = await dbRawRead(this.app, [
      group_type,
      Namespace,
      value,
      SchGroupID
    ], `
        select um.uid as uid, IFNULL(um2.value , 0) as isSameGrade
          from user_metas as um
          join user_roles ur
            on ur.uid = um.uid
           and ur.role_type = 'schl_student'
           and ur.is_revoked = 0
          join school_classes sc
            on sc.group_id = ur.group_id
           and sc.is_active = 1
           and group_type = ?
          join school_semesters sm
            on sm.id = sc.semester_id
          join test_windows tw
            on tw.id = sm.test_window_id
           and tw.is_active = 1
     LEFT JOIN user_metas AS um2
            ON um.uid = um2.uid
           AND um2.key_namespace = 'eqao_sdc'
           AND um2.key = ?
           AND um2.value = '1'
         where um.key = ${isSASN ?'"SASN"':'"StudentOEN"'}
           and um.key_namespace = 'eqao_sdc'
           and um.value = ?
           and sc.schl_group_id = ?
    `)

    if(value === '000000000'||records.length == 0 ||
       records.filter(record => record.uid != studentID).length == 0 ||
       records.filter(record =>record.isSameGrade != 0).length == 0){
      return true;
    }
    return false;
  }

  OENMast = async (value:number):Promise<number> =>{
    var mask:any ={};
    mask[1]=0;  mask[2]=2;  mask[3]=4;  mask[4]=6;  mask[5]=8;  mask[6]=1;  mask[7]=3;  mask[8]=5;  mask[9]=7;  mask[10]=9
    mask[11]=1; mask[12]=3; mask[13]=5; mask[14]=7; mask[15]=9; mask[16]=2; mask[17]=4; mask[18]=6; mask[19]=8; mask[20]=0
    mask[21]=2; mask[22]=4; mask[23]=6; mask[24]=8; mask[25]=0; mask[26]=3; mask[27]=5; mask[28]=7; mask[29]=9; mask[30]=1
    mask[31]=3; mask[32]=5; mask[33]=7; mask[34]=9; mask[35]=1; mask[36]=4; mask[37]=6; mask[38]=8; mask[39]=0; mask[40]=2
    mask[41]=4; mask[42]=6; mask[43]=8; mask[44]=0; mask[45]=2; mask[46]=5; mask[47]=7; mask[48]=9; mask[49]=1; mask[50]=3
    mask[51]=5; mask[52]=7; mask[53]=9; mask[54]=1; mask[55]=3; mask[56]=6; mask[57]=8; mask[58]=0; mask[59]=2; mask[60]=4
    mask[61]=6; mask[62]=8; mask[63]=0; mask[64]=2; mask[65]=4; mask[66]=7; mask[67]=9; mask[68]=1; mask[69]=3; mask[70]=5
    mask[71]=7; mask[72]=9; mask[73]=1; mask[74]=3; mask[75]=5; mask[76]=8; mask[77]=0; mask[78]=2; mask[79]=4; mask[80]=6
    mask[81]=8; mask[82]=0; mask[83]=2; mask[84]=4; mask[85]=6; mask[86]=9; mask[87]=1; mask[88]=3; mask[89]=5; mask[90]=7
    mask[91]=9; mask[92]=1; mask[93]=3; mask[94]=5; mask[95]=7; mask[96]=0; mask[97]=2; mask[98]=4; mask[99]=6; mask[100]=8

    return mask[value];
  }

}
