const _ = require('lodash');
import { Application } from '../../../../declarations';
import { currentUid } from '../../../../util/uid';
import { dbDateToMoment } from '../../../../hooks/_util';
import { Errors } from '../../../../errors/general';
import { generateS3DownloadUrl, generateS3UploadUrl, uploadStrFileToPath } from '../../../upload/upload.listener';
import { generateSecretCode, hashValues } from '../../../../util/secret-codes';
import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { isDatePast, dbDateNow } from '../../../../util/db-dates';
import { ITestSession, EDeliveryOption } from '../../../db/schemas/test_sessions.schema';
import { ITestWindow } from '../../../db/schemas/test_windows.schema';
import { randArrEntry } from '../../../../util/random';
import axios from 'axios';
import { Knex } from 'knex';
import { dbRawRead, dbRawReadCountReporting, dbRawReadReporting, dbRawReadSingle, dbRawWrite } from '../../../../util/db-raw';
import { ITestAttempt, ITestAttemptInfo } from '../../../db/schemas/test_attempts.schema';
import { ITestDesignPayload } from '../../test-taker/invigilation/test-attempt/test-design.data';
import { HEADER_SEB_REQ } from '../../../../constants/headers';
import { sanitizeFramework } from '../../anon/sample-test-design-form/util/sanitize-framework';
import { numAsBool } from '../../../../util/param-sanitization';
import { ISSConfig } from '../session-question/session-question.class';
import Redis from 'ioredis';
import { ALT_VERSION_REQUEST_STATUS } from '../../alt-version-ctrl/alt-version-requests/types'
import { KSubmData, KAttemptPos } from '../../../../redis/redis';
import { sleep } from '../../../../util/timeout';
import { isTestAttemptPaused, patchTestAttempt } from '../../../../redis/table-key-operations/test-attempt';
import { isTestSessionPaused } from '../../../../redis/table-key-operations/test-session';
import { getRedisUserMetaValue } from '../../../../redis/table-key-operations/user-meta'
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';
import { patchTestAttemptSubSession } from '../../../../redis/table-key-operations/test-attempt-sub-session';
import { TestDesignSimilarityTag } from '../../educator/session-sub/session-sub.class';
import Axios from 'axios';


const IS_AUTO_INDENTITY_VERIFIED =  new Set([EDeliveryOption.SCHOOL, EDeliveryOption.SURVEY])
interface Data {}

interface ServiceOptions {}

export interface IGetTestAttemptOptions {
  isCreateNewIfEmpty:boolean,
  isTestTaker:boolean,
  isPresent: boolean
}
export interface ITestAttemptQuestionResponses {
  test_question_id:number,
  response_raw:string
}
export interface IAttemptPayload {
  uid: number,
  test_session_id: number,
  lang?: string,
  created_by_uid?: number,
  delivery_format?: EDeliveryOption
}
export interface ITestDesignInfo {
  test_design_id: Id,
  slug:string,
  source_item_set_id: Id,
  test_form_id: Id,
  alloc_id: Id,
  famework: string,
  delivery_format: EDeliveryOption,
  subsession_meta: string,
  test_form_linear: string
}
export interface IPopNonResConfig {
  cache:any, // todo: what is this exactly?
  resolvedAttempt:any,
}
interface IQueryConfig {
  test_session_id:number,
  lang:string
}

export class Session implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  private async findSchoolsByClassGroupId(schl_class_group_id: number, uid: number) {
    const school = await dbRawRead(this.app, [uid, schl_class_group_id], `
      select s.*
      from schools s
      join school_classes sc
        on s.group_id = sc.schl_group_id
      join user_roles ur
        on ur.group_id = s.group_id
        and uid = ?
        and ur.is_revoked != 1
      where sc.group_id = ?;
      ;
    `);

    //fetch guest_student_school
    if(school.length === 0){
      const guest_student_school = await dbRawRead(this.app, [uid, schl_class_group_id], `
        select s.*
          from schools s
          join school_classes sc on s.group_id = sc.schl_group_id
          join school_classes_guest scg on scg.invig_sc_group_id = sc.group_id and scg.is_revoked != 1
          join user_roles ur on ur.group_id = scg.guest_sc_group_id and uid = ? and ur.is_revoked != 1
         where sc.group_id = ?;
      ;`);
      guest_student_school.forEach(gsd => school.push(gsd))
    }

    return school;
  }

  processSessionPaymentStatus(session: any, school: any) {
    let paymentRequired = 0;
    if(session.slug.includes('G9')) {
      paymentRequired = school.payment_req_g9;
    } else if(session.slug.includes('OSSLT')) {
      paymentRequired = school.payment_req_osslt;
    } else if(session.slug.includes('JUNIOR') || session.slug.includes('PRIMARY')) {
      paymentRequired = school.payment_req_pj;
    }
    console.log('paymentRequired', `${session.slug} r${paymentRequired}? p${session.isPaid}?`)
    session.isPaid = paymentRequired ? session.isPaid : 1;
    console.log('paymentRequiredChecked', `${session.slug} r${paymentRequired}? p${session.isPaid}?`)
    return session
  }

  async verifyStudentPaidForTest(test_session_id: number, uid: number, schl_class_group_id: number) {
    const school = await this.findSchoolsByClassGroupId(schl_class_group_id, uid);
    if (school.length === 0) {
      throw new Errors.NotFound('NO_SCHOOL_ROLE_FOR_USER');
    }

    const test_session = await dbRawRead(this.app, [test_session_id, uid], `
        select scts.slug,
        (CASE
          WHEN (tsp.alternative_status = 3)
            THEN 1
          WHEN scts.slug LIKE '%SAMPLE%'
            THEN 1
          ELSE 0
        END) isPaid
        from school_class_test_sessions scts
        join test_sessions ts on ts.id = scts.test_session_id
        join test_attempts ta on ta.test_session_id = scts.test_session_id
        LEFT JOIN student_attempt_purchases sap
          ON ta.uid = sap.uid
          AND sap.is_revoked != 1
          AND (sap.is_refunded != 1 OR sap.is_refunded IS null)
        LEFT JOIN test_session_purchases tsp
          ON sap.ts_purchase_id = tsp.id
          AND tsp.is_revoked != 1
          AND tsp.test_window_id = ts.test_window_id
        LEFT JOIN student_attempt_purchases sap2
          ON ta.uid = sap2.uid
          AND sap.id < sap2.id
          AND sap2.is_revoked != 1
          AND (sap2.is_refunded != 1 OR sap2.is_refunded IS null)
        LEFT JOIN test_session_purchases tsp2
          ON sap2.ts_purchase_id = tsp2.id
          AND tsp2.is_revoked != 1
          AND tsp2.test_window_id = ts.test_window_id
        where scts.test_session_id = (?)
          and ts.is_cancelled = 0
          and ts.is_closed = 0
          and ta.uid = ?
          and ts.date_time_start < now()
          AND tsp2.id is NULL
        group by ts.id
      ;`);
    if(test_session.length === 0) {
      throw new Errors.NotFound('SESSION_CLOSED');
    }

    const session = this.processSessionPaymentStatus(test_session[0], school[0]);
    if(session.isPaid === 0) {
      throw new Errors.Forbidden('STUDENT_NOT_PAID');
    }
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (!params){
      throw new Errors.BadRequest();
    }
    const {schl_class_group_id, allow_inactive_subsession, schl_class_id} = (<any>params).query;
    if(!schl_class_group_id) {
      throw new Errors.BadRequest('MISSING_REQ_PARAM');
    }
    const uid = await currentUid(this.app, params);

    let classrooms = await this.getData([uid, schl_class_group_id], `
      select sc.id
      from user_roles urt
      join school_classes sc
        on urt.group_id = sc.group_id
        and urt.role_type = 'schl_student'
        and urt.is_revoked != 1
        and sc.is_active = 1
      where urt.uid = ?
      AND sc.group_id = ?
    ;`);

    const inviClassRooms = await this.getData([uid, schl_class_group_id], `
        select sc.id
          from user_roles urt
          join school_classes_guest scg on scg.guest_sc_group_id = urt.group_id and scg.is_revoked != 1
          join school_classes sc on sc.group_id = scg.invig_sc_group_id and sc.is_active = 1
         where urt.role_type = 'schl_student'
           and urt.is_revoked != 1
           and urt.uid = ?
           and sc.group_id = ?
    ;`);
    classrooms = classrooms.concat(inviClassRooms)

    const classIds = classrooms.map(entry => entry.id)

    var classes_sessions:any[] = []
    var scheduled_sessions:any[] = []

    const subsessionRestriction = allow_inactive_subsession ? '' : 'and ta.active_sub_session_id IS NOT NULL'

    if(classIds.length > 0){
      classes_sessions = await this.getData([classIds, uid], `
        select scts.school_class_id, scts.test_session_id, scts.slug, scts.caption, ts.date_time_start, ts.test_window_id, ts.is_paused, twtar.is_questionnaire,
          (CASE
            WHEN (sap.payment_status = 3)
              THEN 1
            WHEN scts.slug LIKE '%SAMPLE%'
              THEN 1
            ELSE 0
          END) isPaid
        from school_class_test_sessions scts
        join test_sessions ts on ts.id = scts.test_session_id
        join test_attempts ta on ta.test_session_id = scts.test_session_id
        left join test_window_td_alloc_rules twtar on twtar.id = ta.twtdar_id
        LEFT JOIN student_attempt_purchases sap
          ON ta.uid = sap.uid
          AND sap.is_revoked != 1
          AND (sap.is_refunded != 1 OR sap.is_refunded IS null)
        LEFT JOIN test_session_purchases tsp
          ON sap.ts_purchase_id = tsp.id
          AND tsp.is_revoked != 1
        LEFT JOIN student_attempt_purchases sap2
          ON ta.uid = sap2.uid
          AND sap.id < sap2.id
          AND sap2.is_revoked != 1
          AND (sap2.is_refunded != 1 OR sap2.is_refunded IS null)
        where scts.school_class_id IN (?)
          and ts.is_cancelled = 0
          and ts.is_closed = 0
          and ta.uid = ?
          ${subsessionRestriction}
          and ts.date_time_start < now()
          AND sap2.id is NULL
        group by ts.id
      ;`);

      scheduled_sessions = await this.getData([classIds], `
        select scts.school_class_id, scts.test_session_id, scts.slug, scts.caption, ts.date_time_start, ts.test_window_id
        from school_class_test_sessions scts
        join test_sessions ts on ts.id = scts.test_session_id
        where scts.school_class_id IN (?)
          and ts.is_cancelled = 0
          and ts.is_closed = 0
          and ts.date_time_start > now()
      ;`);
    }

    const school = await this.findSchoolsByClassGroupId(schl_class_group_id, uid)

    if (school.length === 0) {
      throw new Errors.NotFound('NO_SCHOOL_ROLE_FOR_USER');
    }

    classes_sessions.forEach(session => this.processSessionPaymentStatus(session, school[0]));
    scheduled_sessions.forEach(session => this.processSessionPaymentStatus(session, school[0]));

    //Check student alt version request status if any
    await Promise.all( classes_sessions.map(async session => await this.processstudentAltVersionRequest(session, uid)));
    await Promise.all( scheduled_sessions.map(async session => await this.processstudentAltVersionRequest(session, uid)));

    return [{
      classrooms,
      scheduled_sessions,
      classes_sessions,
    }];
  }

  async processstudentAltVersionRequest(session:any, studentIds:any){
    session.isAltVersionRequestsPending = 0
    session.isAltVersionRequestsApproved = 0
    session.isAltVersionRequestsOperationalSent = 0

    const alt_version_information = await this.app.service('public/school-admin/student').getAltVersionInformation([studentIds], [session.test_window_id]);

    // should only have 1 valid altversion information per student per test window
    if (!alt_version_information.length || !alt_version_information[0].alt_version_requests_status_id) return
    const {alt_version_requests_status_id} = alt_version_information[0]

    if(alt_version_requests_status_id === ALT_VERSION_REQUEST_STATUS.Pending){
      session.isAltVersionRequestsPending = 1
    }
    else if ([ALT_VERSION_REQUEST_STATUS.Approved, ALT_VERSION_REQUEST_STATUS.Operational_Link_Send, ALT_VERSION_REQUEST_STATUS.Shipment_Send].includes(alt_version_requests_status_id)) {
      session.isAltVersionRequestsApproved = 1
      if(alt_version_requests_status_id === ALT_VERSION_REQUEST_STATUS.Operational_Link_Send) session.isAltVersionRequestsOperationalSent = 1
    }
  }

  async get (id: Id, params?: Params): Promise<Data> {
    // throw new Errors.GeneralError('WINDOWS_CLOSED');
    if (params && id){
      const {schl_class_group_id, lang} = (<any>params).query;
      const test_session_id = <number> id;
      const uid = await currentUid(this.app, params);

      await this.verifyStudentPaidForTest(test_session_id, uid, schl_class_group_id);
      
      const targetClassRecords = <Paginated<any>> await this.app.service('db/read/school-classes').find({
        query: {
          group_id: schl_class_group_id,
        }
      });
      const {sessionInfo} = await this.app.service('public/student/lock-down').validateSecurity(params, uid, targetClassRecords.data[0].id);

      // to do: validate access of this user to this test session.
      return this.findAttemptPayload({uid, test_session_id}, params, sessionInfo.is_softlock_enabled_g9, sessionInfo.is_softlock_enabled_g10, sessionInfo.is_softlock_enabled_pj );

    }
    throw new Errors.BadRequest();
  }

  public async allowInsecureSchool(school_class_id:number){
    let allowInsecure = false;

    const schoolClassInfo = await dbRawReadSingle(this.app, {school_class_id}, `
      SELECT 
        tw.type_slug,
        sc.schl_group_id,
        s.*
      FROM school_classes sc
      JOIN school_semesters ss 
        on ss.id = sc.semester_id 
      JOIN test_windows tw 
        ON tw.id = ss.test_window_id 
      JOIN schools s 
        ON s.group_id = sc.schl_group_id 
      WHERE sc.id = :school_class_id;
      `);

    const slug = schoolClassInfo.type_slug;

    if(schoolClassInfo.is_insecure == 1) {
      allowInsecure = true;
    }
    if((slug === 'EQAO_G3P' || slug === 'EQAO_G6J') && schoolClassInfo.is_insecure_pj == 1){
      allowInsecure = true;
    }
    if(slug === 'EQAO_G9M' && schoolClassInfo.is_insecure_g9 == 1){
      allowInsecure = true;
    }
    if(slug === 'EQAO_G10L' && schoolClassInfo.is_insecure_g10 == 1){
      allowInsecure = true;
    }
    return allowInsecure;
  }

  private async loadStudentMetas(uid:number){
    const student_metas = await this.getData([uid], `
      select key_namespace, \`key\`, \`value\`
      from user_metas
      where uid = ?
    ;`);
    const studentMetaMap = new Map();
    student_metas.forEach((meta:any) => {
      studentMetaMap.set(meta.key_namespace+'.'+meta.key, meta.value);
    });
    return studentMetaMap;
  }

  public async loadNewStudentTestDesign(uid:number, test_session_id:Id, twtdar_order:number = 0, testSessionTDSimilarityTags:TestDesignSimilarityTag[], delivery_format?: EDeliveryOption, lang?:string | null, pre_loaded_test_designs:any[] = []) : Promise<ITestDesignInfo>{
    const testSession = await this.app.service('db/read/test-sessions').get(test_session_id)
    const french_Imm_info = await this.getData([test_session_id], `
      select scts.slug as session_slug, sd.*
        from school_class_test_sessions scts
        join school_classes sc on sc.id = scts.school_class_id
        join school_districts sd on sd.group_id = sc.schl_dist_group_id
       where scts.test_session_id = ?
    ;`);

    let schl_dist_fi_otion=''
    let session_slug=''

    if(french_Imm_info[0]){
      schl_dist_fi_otion = french_Imm_info[0].fi_option
      session_slug = french_Imm_info[0].session_slug
    }

    let slug = '';
    // For teacher admin questionnaire
    if(delivery_format === EDeliveryOption.SURVEY){
      const questionnaireTestSession = <Paginated<any>> await this.app
        .service('db/write/questionnaire-test-sessions')
        .find({ query: { test_session_id }})

      const qtsRecord = questionnaireTestSession.data[0]
      slug = qtsRecord.slug
    }
    // For students
    else {
      const schoolClassTestSessions = <Paginated<any>> await this.app
        .service('db/read/school-class-test-sessions')
        .find({ query: { test_session_id, } });
      const sctsRecord = schoolClassTestSessions.data[0]
      slug = sctsRecord.slug;
    }

    const studentMetaMap = await this.loadStudentMetas(uid);

    //Populate eqao_dyn.Linear with 0 when undefined for twtdar rule filtering
    const srcNamespace = slug.includes('PRIMARY') ? 'eqao_sdc_g3' :slug.includes('JUNIOR') ? 'eqao_sdc_g6' :slug.includes('OSSLT') ? 'eqao_sdc_g10' : 'eqao_sdc';

    if(!studentMetaMap.has('eqao_dyn.Linear')){
      studentMetaMap.set('eqao_dyn.Linear', studentMetaMap.get(`${srcNamespace}.Linear`))
    }

    if(studentMetaMap.get('eqao_dyn.Linear') == null || studentMetaMap.get('eqao_dyn.Linear') == '#') {
      studentMetaMap.set('eqao_dyn.Linear', '0')
    }

    //School class tests sessions to which altervative requests can apply
    const AllowedSessions = [
      'PRIMARY_OPERATIONAL',   'PRIMARY_SAMPLE',
      'JUNIOR_OPERATIONAL',    'JUNIOR_SAMPLE',
      'G9_OPERATIONAL',        'G9_SAMPLE',
      'OSSLT_OPERATIONAL',     'OSSLT_SAMPLE',
    ]

    if(AllowedSessions.includes(session_slug)) {
      const alt_version_information =  await this.app.service('public/school-admin/student').getAltVersionInformation([uid], [testSession.test_window_id]);
      const alt_version_status = alt_version_information[0]?.alt_version_requests_status_id
      const alt_version_req_approved = (alt_version_information.length > 0 && (alt_version_status == ALT_VERSION_REQUEST_STATUS.Approved || alt_version_status == ALT_VERSION_REQUEST_STATUS.Operational_Link_Send))
      // if no alt_version_information, or alt request or alt request is not approved
      if(studentMetaMap.get('eqao_dyn.Linear') !== '0'){
        if(!alt_version_information.length || !alt_version_req_approved){
          studentMetaMap.set('eqao_dyn.Linear', '0')
        }
      }
      // if have alt_version_information, and alt request is approved set linear from 0 to 1 if its 0.
      //(This should not happen because when school admin sent alt request, it will set the Linear to relate value but put it here just in case)
      if(studentMetaMap.get('eqao_dyn.Linear') === '0'){
        if(alt_version_information.length && alt_version_req_approved){
          const { newUserMetaLinaerValue } = await this.app.service('public/school-admin/alternative-version-request').getLinearValue(testSession.test_window_id, uid, alt_version_information );
          studentMetaMap.set('eqao_dyn.Linear', newUserMetaLinaerValue)
        }
      }
    }

    if((session_slug == 'PRIMARY_OPERATIONAL'||session_slug == 'PRIMARY_SAMPLE')){
      if(schl_dist_fi_otion == 'B'||schl_dist_fi_otion == 'C'){
        if(studentMetaMap.get(`${srcNamespace}.FrenchImmersion`) && studentMetaMap.get(`${srcNamespace}.FrenchImmersion`) == '1'){ //only
          if(twtdar_order == 1){ //only math
            studentMetaMap.set('eqao_dyn.Lang', 'fr')
          }
        }
      }
    }

    const test_window_td_alloc_rules = await this.getData([testSession.test_window_id, slug, twtdar_order], `
      select *
      from test_window_td_alloc_rules twtdar
      where twtdar.test_window_id = ?
        and twtdar.type_slug = ?
        and twtdar.order = ?
    ;`);

    if (test_window_td_alloc_rules.length === 0) {
      throw new Errors.BadGateway('NO_TEST_DESIGN_RULE_MATCH');
    }

    let testDesignMatch:{test_design_id:Id, id:Id, subsession_meta:any, is_classroom_common_form:number};
    if(this.app.get('context').isBCED){
      testDesignMatch = test_window_td_alloc_rules[0];
    } else {
      let isTestDesignMatchFound:boolean = false;
      testDesignMatch = {id:-1, test_design_id:-1, subsession_meta: undefined, is_classroom_common_form:0};
      test_window_td_alloc_rules.forEach(testDesignCandidate => {
        const filters:{[key:string]:Array<string|number>} = JSON.parse(testDesignCandidate.user_metas_filter);
        let isAllRulesPassed = true;
        Object.keys(filters).forEach(key => {
          const possibleValues = filters[key].map(val => ''+val);
          // For questionnair used lang filter for now
          // #TODO: Maybe insert teacher/student record in user_metas with `lang` as key
          const val = (delivery_format === EDeliveryOption.SURVEY && lang) ? lang : ''+studentMetaMap.get(key);
          if (possibleValues.length > 0){
            if (possibleValues.indexOf(val) === -1){
              isAllRulesPassed = false;
            }
          }
          else {
            if (val && val !== '#'){
              isAllRulesPassed = false;
            }
          }
        });
        if (isAllRulesPassed){
          isTestDesignMatchFound = true;
          testDesignMatch = testDesignCandidate
        }
      });
      if (!isTestDesignMatchFound){
        throw new Errors.BadGateway('NO_TEST_DESIGN_RULE_MATCH');
      }
    }
    let testDesignRecord
    if(pre_loaded_test_designs.length){
      testDesignRecord = pre_loaded_test_designs.find(pre_loaded_test_design => +pre_loaded_test_design.id === +testDesignMatch.test_design_id)
    }
    if(!testDesignRecord){
      testDesignRecord = await this.app .service('db/read/test-designs').get(testDesignMatch.test_design_id);
    }
    if (!testDesignRecord){
      throw new Errors.NotFound();
    }

    const testDesignId = testDesignMatch.test_design_id
    const testFormRefsData = await dbRawReadReporting (this.app, {testDesignId}, `
      -- took 156 ms to run on mirror db
      select tf.id
           , tf.lang
        from test_forms tf
       where tf.test_design_id = :testDesignId
         and tf.is_revoked = 0
       limit 1000
    ;`)
    const testFormRefs = {
      data: testFormRefsData,
      total: testFormRefsData.length
    }

    const testFormRefIds =   testFormRefs.data.map(testFormRef => {return testFormRef.id})
    //filter testFormRefs by associate testSessionTDSimilarityTag
    //when testSessionTD.similarity_tag is null/undefine this mean to filter test forms that have undefined similarity slug
    const testSessionTD = testSessionTDSimilarityTags.find (testSessionTDSimilarityTag => +testSessionTDSimilarityTag.test_design_id === +testDesignMatch.test_design_id)
    if(testSessionTD){
      const similarity_tag = testSessionTD.similarity_tag
      // TODO this query isn't needed - it could just be merged with the one above to get testFormRefsData
      const testFormsWithSameSimilarityTags = await dbRawReadReporting(this.app, {testFormRefIds, similarity_tag }, `
         -- took ~500ms when running on mirror db with 798 testFormRefIds
          select distinct
                 tf.id as test_form_id
            from test_forms tf
       left join historic_form_tags hft on hft.test_form_id = tf.id and hft.is_revoked = 0
           where tf.is_revoked = 0
             and tf.id in (:testFormRefIds)
           ${similarity_tag? 'and hft.similarity_tag = :similarity_tag': 'and hft.id is null'}
      ;`)
      const test_form_ids = testFormsWithSameSimilarityTags.map(testFormsWithSameSimilarityTag => {return testFormsWithSameSimilarityTag.test_form_id})
      testFormRefs.data = testFormRefs.data.filter( (testForm:any) => test_form_ids.includes(testForm.id))
      testFormRefs.total = testFormRefs.data.length;
    }else{
      // throw error when Test Design's similarity tag was not fetched in function "getTestSessionTDSimilarityTags" in "public/educator/session-sub"
      // Not going to catch on client since this is data setting error not user usage error.
      throw new Errors.BadRequest('NO_SIMILARITY_TEST_DESIGN_FOUND');
    }

    // use common test form when test_window_td_alloc_rules's is_classroom_common_form is 1
    let testFormSelection;
    if(testDesignMatch.is_classroom_common_form === 1 && test_session_id){
      const school_class_common_forms = <any[]>await dbRawRead(this.app, [test_session_id, testDesignMatch.id], `
        select sccf.*
          from school_class_common_forms sccf
          join school_class_test_sessions scts
            on scts.school_class_id = sccf.school_class_id
           and scts.test_session_id = ?
         where sccf.twtdar_id = ?
           and sccf.is_revoked != 1
      ;`);
      if(school_class_common_forms && school_class_common_forms.length > 0){
        testFormSelection = await this.app .service('db/readreporting/test-forms').get(school_class_common_forms[0].test_form_id);
      }
    }

    if(testFormSelection === undefined){
      testFormSelection = randArrEntry(testFormRefs.data);
    }

    return {
      test_design_id: testDesignMatch.test_design_id,
      slug,
      source_item_set_id: testDesignRecord.source_item_set_id,
      test_form_id: testFormSelection.id,
      alloc_id: testDesignMatch.id,
      famework: testDesignRecord.framework,
      delivery_format: testSession.delivery_format,
      subsession_meta: testDesignMatch.subsession_meta,
      test_form_linear: studentMetaMap.get('eqao_dyn.Linear')
    }
  }

  public async loadTestDesignForm(test_design_id:number){
    return {
      test_design_id,
      // testFormData,
      // framework: testDesignRecord.framework,
    };
  }

  public async getTestDesign(file_path:string, testFormId?:number, sourceFormId?:number){
    const testDesign = await this.getTestDesignPromise(file_path);
    return <any> {
      testFormId,
      sourceFormId,
      ... testDesign.data
    };
  }

  public getTestDesignPromise(file_path: string) {
    const url = generateS3DownloadUrl(file_path, 60);
    return axios.get(url, {});
  }

  private async ensureAttemptMarkedStarted(currentAttempt:ITestAttempt){
    if (!currentAttempt.started_on){
      const dbNow = dbDateNow(this.app);
      await this.app
        .service('db/write/test-attempts')
        .patch(currentAttempt.id, {
          started_on: dbNow,
          last_touch_on: dbNow,
        });
    }
  }

  private async getAttemptQuestionResponses(test_attempt_id:number){
    // await this.app.service('public/student/session-question').removeDuplicateQuestionAttemptRecords(test_attempt_id); // this is not necessary because we are anyway pulling the records in order of id
    const questionResponseStates = <ITestAttemptQuestionResponses[]> await this.app
      .service('db/read/test-attempt-question-responses')
      .find({query:{
        test_attempt_id,
        is_invalid: 0,
      }, paginate: false});
    const questionStates:{[key:string]: string} = {};
    questionResponseStates.forEach(q => {
      questionStates[q.test_question_id] = JSON.parse(q.response_raw)
    });

    const redis: Redis = this.app.get('redis'); // Redis is now enforced
    const submData = await redis.hgetall(KSubmData(test_attempt_id));
    for (let questId in submData) {
      const questSubm = JSON.parse(submData[questId]);
      if (typeof questSubm.response_raw === 'string') {
        questionStates[questId] = JSON.parse(questSubm.response_raw);
      } else {
        questionStates[questId] = questSubm.response_raw;
      }
    }

    return questionStates;
  }

  private async getAttemptTimeInfo(currentAttempt:ITestAttempt){
    const testSession = <ITestSession> await this.app.service('db/read/test-sessions').get(currentAttempt.test_session_id);
    const testWindow =  <ITestWindow> await this.app.service('db/read/test-windows').get(testSession.test_window_id);
    const user_time_ext_m = currentAttempt.time_ext_m || 0;
    const test_session_time_ext_m = testSession.time_ext_m || 0;
    const test_window_time = testWindow.duration_m || 0;
    return {
      test_window_time,
      time_ext_m: user_time_ext_m + test_session_time_ext_m
    }
  }

  public async loadAttemptTestFormData(currentAttempt:ITestAttempt){
    let test_form_cache = {};
    if (currentAttempt.test_form_cache){
      try {
        test_form_cache = JSON.parse(currentAttempt.test_form_cache);
      }
      catch(e){}
    }
    const testFormData = await this.loadTestFormById(currentAttempt.test_form_id);
    return {
      test_form_id: currentAttempt.test_form_id,
      ... testFormData,
      ... test_form_cache, // because this one comes second, it will override any properties that have the same name in the pristine static test form payload (which is loaded from S3)
    }
  }

  async loadStyleProfileByTestDesign(test_form_id:number){
    if(!test_form_id){
      return {styleProfileId: undefined};
    }
    //? QC4 duration 0.017 sec / 0.0000069 sec
    const tdStyleProfileRecs = <any>await dbRawReadSingle(this.app, {test_form_id}, `
        SELECT td.style_profile_id as id, 
        td.profile_path as td_profile_path,
        sp.s3_link as sp_profile_path,
        sp.slug as slug
        FROM test_forms tf
        JOIN test_designs td ON tf.test_design_id = td.id
        JOIN style_profiles sp ON td.style_profile_id = sp.id
        WHERE tf.id = :test_form_id;
      `);

    // Style profile path is fallback if the test design doesn't have an associated one
    const profilePath = tdStyleProfileRecs?.td_profile_path ?? tdStyleProfileRecs.sp_profile_path;
    if(profilePath){
      try{
        const s3styleProfileUrl = await generateS3DownloadUrl(profilePath);
        return {
          styleProfileId: tdStyleProfileRecs.id,
          slug: tdStyleProfileRecs.slug,
          url: s3styleProfileUrl,
        }
      } catch (e){
        return {
          error: e
        }
      }
    }
    return {styleProfileId: tdStyleProfileRecs.id, slug: tdStyleProfileRecs.slug};
  }

  private async loadFrameworkByTestformId(test_form_id:number){
    // try to load framework from S3
    let framework
    const filePath = `testform_td_framework/test_form_id_${test_form_id}.json`
    const url = generateS3DownloadUrl(filePath, 60);
    try{
      const file = await Axios.get(url, {})
      framework = JSON.stringify(file.data)
    }catch{
      //can't fetch framework from S3, fatch from db
      //do nothing
    }

    // can't find framework on S3
    if(!framework){
      //fetch framework from db
      const records = await dbRawRead(this.app, [test_form_id], `
        select td.framework
          from test_forms tf
          join test_designs td on td.id = tf.test_design_id
         where tf.id = ?
      ;`);
      framework = sanitizeFramework(records[0].framework)

      //stored test form td framework into S3 for future use
      await uploadStrFileToPath(filePath, framework);
    }
    return framework
  }

  private async getDeliveryFormat(test_session_id:number){
    const deliveryFormatInfo = await this.app.service('db/read/test-sessions')
      .db()
      .select('delivery_format')
      .where('id', test_session_id)
    return deliveryFormatInfo[0].delivery_format;
  }

  public async findAttemptPayload(data: IAttemptPayload,params: Params, is_softlock_enabled_g9:boolean, is_softlock_enabled_g10:boolean, is_softlock_enabled_pj:boolean){
    const { uid, test_session_id, lang} = data;
    const created_by_uid = data.created_by_uid || uid;
    const isOnBeHalf = (created_by_uid !== uid);
    const delivery_format = await this.getDeliveryFormat(test_session_id);
    const isCreateNewIfEmpty = delivery_format === EDeliveryOption.SURVEY ? true : false
    const currentAttempt:ITestAttempt = await this.getCurrentAttempt(uid, test_session_id, {isCreateNewIfEmpty, isTestTaker:!isOnBeHalf, isPresent: true}, delivery_format, lang);
    // await this.identifyPreAssessmentStep(currentAttempt, true); // if this does not throw an error, it means there are no more pre-setup steps left to be done
    await this.ensureAttemptMarkedStarted(currentAttempt);
    const testDesign:ITestDesignPayload = await this.loadAttemptTestFormData(currentAttempt);
    const questionStates = await this.getAttemptQuestionResponses(currentAttempt.id);
    const framework = await this.loadFrameworkByTestformId(currentAttempt.test_form_id);
    const styleProfile = await this.loadStyleProfileByTestDesign(currentAttempt.test_form_id);
    const is_issue_reporting_enabled =  0; // todo: move this into the test window configuration
    const {time_ext_m, test_window_time} = await this.getAttemptTimeInfo(currentAttempt);
    const subSessionData = await this.validateSubSessionAttempt(currentAttempt.id, currentAttempt, delivery_format);

    // If Redis is enabled, fetch the attempt position from Redis cache if available
    const redis: Redis = this.app.get('redis');
    if(redis) {
      const attemptPos = await redis.get(KAttemptPos(currentAttempt.id));
      const attemptPosData = JSON.parse(attemptPos || '{}');
      if(attemptPosData) {
        currentAttempt.section_index = attemptPosData.section_index || currentAttempt.section_index;
        currentAttempt.question_index = attemptPosData.question_index || currentAttempt.question_index;
      }
    }
    currentAttempt.section_index = currentAttempt.section_index || 0;
    currentAttempt.question_index = currentAttempt.question_index || 0;

    let subsession_index = undefined;
    let sections_allowed;
    if (subSessionData){
      subsession_index = subSessionData.order;
      if (subSessionData.activeSubSessionAttempt){
        const tass = subSessionData.activeSubSessionAttempt;
        if(tass.sections_allowed) {
          sections_allowed = JSON.parse(tass.sections_allowed);
        }
        if (!tass.started_on){
          const payload:any = {
            started_on: new Date().toISOString(),
          }

          await patchTestAttemptSubSession(this.app, tass.id, payload)
        }
      }

      if(!sections_allowed) {
        sections_allowed = subSessionData.sections_allowed; //Used temporarily for transitioning to new logic - now should use tass.
      }
      if (currentAttempt.section_index < sections_allowed[0]){
        currentAttempt.section_index = sections_allowed[0];
        currentAttempt.question_index = 0;
        currentAttempt.module_id = undefined;
      }
    }
    let isSebMode = false;
    if (params.headers && params.headers[HEADER_SEB_REQ]){
       isSebMode = true;
    }

    const db = this.app.get('knexClientRead')

    const asmtRecs = await db('school_class_test_sessions as scts')
    .join('school_classes as sc', 'sc.id', 'scts.school_class_id')
    .where('scts.test_session_id', test_session_id)
    .select('slug', 'group_type')

    let asmt_slug;
    let group_type;

    if(asmtRecs?.length) {
      asmt_slug = asmtRecs[0].slug;
      group_type = asmtRecs[0].group_type;
    }
    const assistiveTech = await this.verifyAssistedTechAccomodation(uid, group_type, asmt_slug)
    const linear = await this.getLinear(uid, group_type);
    const IsCrScanDefault = await this.getUserMetaProp(uid, group_type, 'IsCrScanDefault');

    return [
      {
        is_softlock_enabled_g9,
        is_softlock_enabled_g10,
        is_softlock_enabled_pj,
        lang: currentAttempt.lang,
        question_index: currentAttempt.question_index,
        section_index: currentAttempt.section_index,
        module_id: currentAttempt.module_id,
        attempt_key: currentAttempt.attempt_key,
        attemptId: currentAttempt.id,
        assistiveTech,
        linear,
        isSebMode,
        attempt_twtdar_order: currentAttempt.twtdar_order,
        framework,
        testDesign,
        is_issue_reporting_enabled,
        questionStates,
        time_ext_m,
        test_window_time,
        sections_allowed,
        subsession_index,
        asmt_slug,
        IsCrScanDefault,
        styleProfile
      }
    ];
  }

  private async identifyPreAssessmentStep(currentAttempt:ITestAttempt, markAsPresent:boolean){
    // ensure tt is marked as present
    if (markAsPresent && currentAttempt.is_present === 0){
      await this.app
        .service('db/write/test-attempts')
        .patch(currentAttempt.id, {
          is_present: 1,
          is_absent: 0,
        });
    }
    // ensure tt has accepted attestation (to do: this should only apply if the test session requires attestation)
    if (!currentAttempt.is_attested){
      throw new Errors.Forbidden('ACCEPT_ATTEST');
    }
    // ensure tt has identified their language (to do: this might be fine to leave as is when language is pre-selected for tt, as long as it is indicated in the test-attempt entry by the system before this request is made)
    if (!currentAttempt.lang){
      throw new Errors.Forbidden('SELECT_LANG');
    }
  }

  getNamespace(group_type: string) {
    switch(group_type) {
      case 'EQAO_G3':
        return 'eqao_sdc_g3';
      case 'EQAO_G6':
        return 'eqao_sdc_g6';
      case 'EQAO_G9':
        return 'eqao_sdc';
      case 'EQAO_G10':
        return 'eqao_sdc_g10';
    }
    return 'eqao_sdc';
  }

  async getUserMetaProp(uid: number, group_type: string, key:string, asNum?: boolean, loadFromRedis = false) {
    const namespace = this.getNamespace(group_type);
    let values:any[] = []
    if(loadFromRedis){
      const userMetaValue = await getRedisUserMetaValue(this.app, uid, namespace, key);
      if(userMetaValue){
        values.push(userMetaValue)
      }
    }else{
      values = await this.app.service('db/read/user-metas').db().where('uid', uid).where('key', key).where('key_namespace', namespace).pluck('value');
    }

    if(!values?.length && asNum) {
      return asNum ? 0 : null;
    }

    let val = values[0];

    if(!asNum) {
      return val;
    }

    if(!val || val === '#') {
      val = 0;
    } else {
      val = +val;
    }
    return val;
  }


  async getLinear(uid: number, group_type: string , loadFromRedis = false) {
    const asNum = true
    return this.getUserMetaProp(uid, group_type, 'Linear', asNum, loadFromRedis)
  }

  async verifyAssistedTechAccomodation(uid:number, group_type: string, asmt_slug?: string, loadFromRedis = false){
    const namespace = this.getNamespace(group_type);
    let keyToSearch = ['AccAssistiveTech'];

    if (asmt_slug) {
      if (asmt_slug === 'PRIMARY_OPERATIONAL' || asmt_slug === 'JUNIOR_OPERATIONAL') {
        keyToSearch = [];
        keyToSearch.push('AccAssistiveTechMath', 'AccAssistiveTechRead', 'AccAssistiveTechWrite');
      }
    }

    if(loadFromRedis){
      const user_meta_values = ['1', '2']
      for (let i =0 ; i< keyToSearch.length; i++){
        const key = keyToSearch[i]
        const metaValue = await getRedisUserMetaValue(this.app, uid, namespace, key);
        if(metaValue && user_meta_values.includes(metaValue)){
          return true
        }
      }
      return false
    }else{
      const studentRecord = await dbRawRead(this.app, [uid, keyToSearch, namespace], `
        select um.*
          from mpt_dev.user_metas um
         where um.uid = ?
           and um.key in (?)
           and um.value IN ('1','2')
           and um.key_namespace = ?`);
      return !!studentRecord.length
    }
  }

  private async validateAttemptWindowAndTiming(attemptRecord:ITestAttemptInfo, testSession:ITestSession){

    const test_session_time_ext_m = attemptRecord.session_time_ext_m || 0;
    const user_time_ext_m = attemptRecord.time_ext_m || 0;
    let momentTimeStart = dbDateToMoment(attemptRecord.date_time_start);
    const testWindow =  <ITestWindow> await this.app.service('db/read/test-windows').get(testSession.test_window_id);
    const isActive = testWindow.is_active;
    const isArchived = testWindow.is_archived;
    if (!isActive){
      throw new Errors.Forbidden('TEST_WINDOW_INACTIVE');
    }
    if (isArchived){
      throw new Errors.Forbidden('TEST_WINDOW_ARCHIVED');
    }
    const test_window_time = testWindow.duration_m || 0;
    const timeClose = momentTimeStart.add(test_session_time_ext_m+user_time_ext_m+test_window_time, 'minutes');
    if (isDatePast(timeClose.format())){
      throw new Errors.Forbidden('TIME_OUT');
    }
  }

  public async validateSubSessionAttempt(attempt_id:number, attemptRecord?:ITestAttempt, delivery_format?: EDeliveryOption, subsession_config?: ISSConfig, section_index?:number){
    // todo: check if in a sub session structure test session
    if(delivery_format === EDeliveryOption.SURVEY) return null;
    await sleep(100); // sleep for 100ms to ensure that the read replica is current
    if (!attemptRecord){
      attemptRecord = <ITestAttempt> await this.app.service('db/readreporting/test-attempts').get(attempt_id);
    }

    let subsession_slug: any = '';
    let subsession_order: any = '';

    if (subsession_config) {
      subsession_slug = subsession_config.subsession_slug;
      subsession_order = subsession_config.subsession_order;
    }

    let tsssId = null;
    if (subsession_slug && subsession_slug.length > 0) {
      let tsssSlug = subsession_slug;
      // let tsssRecord;

      const tsssRec = await this.app.service('db/read/test-session-sub-sessions')
        .db()
        .where('test_session_id', attemptRecord.test_session_id)
        .where('slug', tsssSlug)
        .where('order', subsession_order);
      if(tsssRec[0]){
        tsssId = tsssRec[0].id;
      }
    }

    const active_sub_session_id = tsssId ? tsssId : attemptRecord.active_sub_session_id;
    if (active_sub_session_id === null){
      throw new Errors.Forbidden('SESSION_CLOSED');
    }

    // osslt/G9 section_allowed saved in tsss
    // Primary section_allowed saved in tass
    // TASS should take priority over TSSS
    const subSession = <any> await this.app.service('db/readreporting/test-session-sub-sessions').get(<number> active_sub_session_id);
    let sections_allowed;

    const subSessionAttempts = <any> await this.app.service('db/readreporting/test-attempt-sub-sessions').find({query:{test_attempt_id:attempt_id, sub_session_id:active_sub_session_id, is_invalid: {$ne: 1}}, paginate: false});
    const activeSubSessionAttempt = subSessionAttempts[0];
    if(activeSubSessionAttempt?.sections_allowed){
      sections_allowed = JSON.parse(activeSubSessionAttempt.sections_allowed || '[]')
    }

    if((!sections_allowed || !sections_allowed.length) && subSession.sections_allowed) {
      sections_allowed = JSON.parse(subSession.sections_allowed);
    }
    const is_last:number = subSession.is_last;
    const is_last_bool = is_last === 1 ? true : false;

    // check if the question section is allowing the in activeSubsession (check if test taker is writting in the locked subsession)
    if(section_index !== undefined && !sections_allowed.includes(section_index)){
      throw new Errors.Forbidden('SESSION_CLOSED');
    }
    return {activeSubSessionAttempt, sections_allowed, is_last: is_last_bool, order: subSession.order};
    // attemptRecord.active_sub_session_id = active_sub_session_id;
    // attemptRecord.sections_allowed = sections_allowed;
    // if (attemptRecord.section_index < sections_allowed[0]){
    //   attemptRecord.section_index = sections_allowed[0];
    //   attemptRecord.question_index = 0;
    // }
  }

  public async validateAttemptId(uid:number, attempt_id:number, isOnBehalf:boolean=false, subsession_config?: ISSConfig, section_index?:number,){
    const attemptRecord = (await dbRawReadReporting(this.app, [attempt_id],
      `SELECT
          ta.id AS id
        , ta.uid AS uid
        , ta.test_session_id AS test_session_id
        , ta.lang AS lang
        , ta.booking_lang AS booking_lang
        , ta.section_index AS section_index
        , ta.question_index AS question_index
        , ta.attempt_key AS attempt_key
        , ta.test_form_cache AS test_form_cache
        , ta.created_on AS created_on
        , ta.started_on AS started_on
        , ta.is_closed AS is_closed
        , ta.closed_on AS closed_on
        , ta.is_present AS is_present
        , ta.is_absent AS is_absent
        , ta.is_identity_verified AS is_identity_verified
        , ta.is_identity_missing AS is_identity_missing
        , ta.is_submitted AS is_submitted
        , ta.time_ext_m AS time_ext_m
        , ta.is_seb_downloaded AS is_seb_downloaded
        , ta.is_open_tether AS is_open_tether
        , ta.is_zoom_setup AS is_zoom_setup
        , ta.is_zoom_installed AS is_zoom_installed
        , ta.is_seb_app_downloaded AS is_seb_app_downloaded
        , ta.is_mic_tested AS is_mic_tested
        , ta.is_video_tested AS is_video_tested
        , ta.is_seb_setup AS is_seb_setup
        , ta.is_seb_tested AS is_seb_tested
        , ta.twtdar_order AS twtdar_order
        , ta.is_paused AS is_paused
        , ta.is_checklist_reminder_sent AS is_checklist_reminder_sent
        , ta.has_supported_device AS has_supported_device
        , ta.is_results_released AS is_results_released
        , ts.is_cancelled AS is_session_cancelled
        , ts.time_ext_m AS session_time_ext_m
        , ts.is_closed AS is_session_closed
        , ts.date_time_start AS date_time_start
        , ts.instit_group_id AS instit_group_id
        , ts.delivery_format AS delivery_format
        , ts.is_video_conference AS is_video_conference
        , ac.is_no_td_order AS is_no_td_order
        , tw.is_closed is_tw_closed
  FROM test_attempts ta
  JOIN test_sessions ts ON ts.id = ta.test_session_id
  JOIN test_windows tw ON tw.id = ts.test_window_id
  LEFT JOIN school_class_test_sessions scts ON scts.test_session_id = ts.id
  LEFT JOIN assessment_components ac ON (ac.assessment_code = scts.slug
    AND ac.test_window_id = ts.test_window_id )
  WHERE ta.id = ?`))[0];
    if (!attemptRecord){
      throw new Errors.Forbidden('NOT_BOOKED_APPL');
    }
    if (!attemptRecord.is_identity_verified && !isOnBehalf){
      throw new Errors.Forbidden('NOT_VERIFIED');
    }
    if (attemptRecord.is_closed){
      throw new Errors.Forbidden('ATTEMPT_CLOSED');
    }
    if (attemptRecord.is_identity_missing && !isOnBehalf){
      throw new Errors.Forbidden('MARKED_NO_ID');
    }
    if (attemptRecord.is_absent && !isOnBehalf){
      throw new Errors.Forbidden('MARKED_ABSENT');
    }
    if (attemptRecord.is_session_closed){
      throw new Errors.Forbidden('SESSION_CLOSED');
    }
    const is_paused = await isTestSessionPaused(this.app, attemptRecord.test_session_id)
    if (is_paused){
      throw new Errors.Forbidden('NOT_VERIFIED');
    }
    if (attemptRecord.is_session_cancelled == 1){
      throw new Errors.Forbidden('SESSION_CANCELLED');
    }
    if (attemptRecord.is_tw_closed){
      throw new Errors.Forbidden('SESSION_CLOSED');
    }
    if (+attemptRecord.uid !== +uid){
       throw new Errors.Forbidden('UID_NOT_MATCH');
    }

    // if (attemptRecord.delivery_format !== EDeliveryOption.SCHOOL && attemptRecord.delivery_format !== EDeliveryOption.SURVEY){
    //   await this.validateAttemptWindowAndTiming(attemptRecord, testSession);
    // }

    const is_attempt_paused = await isTestAttemptPaused(this.app, attempt_id)
    if (is_attempt_paused){
      throw new Errors.Forbidden('ATTEMPT_PAUSED');
    }

    let subSessionData:{activeSubSessionAttempt:any, is_last: boolean, order: number} | null = null;
    if (attemptRecord.delivery_format === EDeliveryOption.SCHOOL){
      subSessionData = await this.validateSubSessionAttempt(attempt_id, undefined, undefined, subsession_config, section_index);
    }

    // this would indicate that it is the first time that this student is accessing this attempt
    return {attemptRecord, subSessionData};
  }



  // likely needs to be somewhat re-written
  async getRandomTestFormId(test_session_id:number){
    const availableTestForms = <Paginated<any>> await this.app.service('db/read/test-session-test-forms').find({query: {test_session_id, $limit:1000} });
    const i = Math.floor(availableTestForms.data.length * Math.random());
    const test_form_id = availableTestForms.data[i].test_form_id;
    return test_form_id;
  }

  private async loadTestFormById(test_form_id:Id){
    const testFormRecord = await this.app.service('db/readreporting/test-forms').get(test_form_id);
    return this.getTestDesign(testFormRecord.file_path, testFormRecord.id, testFormRecord.source_tf_id);
  }

  public async generateTestFormCache(testDesign:ITestDesignInfo) : Promise<string>{
    let cache:{[key:string]:any} = {};
    try {
      // temp: these are temporary... just need to cycle out the forms after completing this ticket: https://bubo.vretta.com/vea/platform/vea-web-client/-/issues/2246
      const framework = JSON.parse(testDesign.famework);
      cache.testFormType = framework.testFormType;
      cache.isTimerDisabled = framework.isTimerDisabled;
      cache.referenceDocumentPages = framework.referenceDocumentPages;
      cache.helpPageId = framework.helpPageId;
    }catch(e){}
    const testFormData = await this.loadTestFormById(testDesign.test_form_id);
    try {
      if (testFormData.panelModules){
        testFormData.panelModules.forEach((panelModule:{questions:number[]}) => {
          panelModule.questions = _.shuffle(panelModule.questions);
        })
        cache.panelModules = testFormData.panelModules
      }
    }catch(e){}
    return JSON.stringify(cache);
  }


  private async createAttempt(uid:number, isTestTaker:boolean, test_session_id:number, lang:string | null, testSessionTDSimilarityTags:TestDesignSimilarityTag[], is_present:number = 1, twtdar_order = 0, delivery_format?:EDeliveryOption, from_session_init:boolean = false, pre_loaded_test_designs:any = undefined){
    // const test_form_id = await this.getRandomTestFormId(test_session_id);
    const testDesign = await this.loadNewStudentTestDesign(uid, test_session_id, twtdar_order, testSessionTDSimilarityTags, delivery_format, lang, pre_loaded_test_designs);
    const test_form_id = <number> testDesign.test_form_id;
    const test_form_cache = await this.generateTestFormCache(testDesign);
    const createFields:Partial<ITestAttempt> = {
      uid,
      test_session_id,
      lang: <any>lang,
      section_index: 0,
      question_index: 0,
      test_form_id,
      test_form_cache,
      attempt_key: generateSecretCode(5),
      is_present,
      is_absent: 0,
      twtdar_order,
      twtdar_id: <number>testDesign.alloc_id,
      test_form_linear: testDesign.test_form_linear
    }
    if (IS_AUTO_INDENTITY_VERIFIED.has(testDesign.delivery_format)){
      createFields.is_identity_verified = 1;
    }
    if (isTestTaker){
      createFields.started_on = dbDateNow(this.app);
    }

    if(from_session_init){
      return { data:createFields, subsession_meta:testDesign.subsession_meta }
    }else{
      return this.app
      .service('db/write/test-attempts')
      .create(createFields).then((value) => {
        return {
          ...value,
          subsession_meta: testDesign.subsession_meta
        }
    })
    }

  }

  public validateSessionKey(uid:number, test_session_id:number, timestamp_start:number, sessionHash:string){
    return (sessionHash === this.generateSessionKey(uid, test_session_id, timestamp_start));
  }

  public  generateSessionKey(uid:number, test_session_id:number, timestamp_start:number){
    const sessionHash = hashValues([uid, test_session_id, timestamp_start])
    return sessionHash
  }

  public async createAttemptByTeacher(uid:number, test_session_id:number, testSessionTDSimilarityTags:TestDesignSimilarityTag[], twtdar_order:number = 0, from_session_init:boolean = false, pre_loaded_test_designs:any[] = [])  {
    const delivery_format = undefined
    return this.createAttempt(uid, false, test_session_id, null, testSessionTDSimilarityTags, 0, twtdar_order, delivery_format,  from_session_init, pre_loaded_test_designs);
  }

  private async getCurrentAttempt(uid:number, test_session_id:number, options:IGetTestAttemptOptions,  delivery_format?: EDeliveryOption, lang?:string | null)  {

    const numSubSessionsRows: any = await this.app.service('db/read/test-session-sub-sessions')
    .db()
    .where('test_session_id', test_session_id)
    .count()

    let numSubSessions = 0;
    if(numSubSessionsRows.length > 0) {
      // numSubSessions = numSubSessionsRows[0].count as number;
      numSubSessions = (numSubSessionsRows[0]["count(*)"] as number) || (numSubSessionsRows[0].count as number);
    }

    let query:any = {uid, test_session_id, $limit:1};

    if(numSubSessions > 0) {
      query["active_sub_session_id"] = {$ne: null};
      options.isCreateNewIfEmpty = false;
    }

    const attempts = <ITestAttempt[]> await this.app
      .service('db/read/test-attempts')
      .find({query: {
        uid,
        test_session_id,
        $limit:1,
        active_sub_session_id: numSubSessions > 0 ? {$ne: null} : null,
        is_invalid: {$ne: 1}
      }, paginate: false })
    if (attempts.length > 0){
      if(delivery_format === EDeliveryOption.SURVEY){
        await this.validateAttemptId(attempts[0].uid, attempts[0].id);
      }
      return attempts[0];
    }

    const testSessionTDSimilarityTags = await this.app.service('public/educator/session-sub').getTestSessionTDSimilarityTags(test_session_id, [uid])
    if (options.isCreateNewIfEmpty){
      const present_bool_int = options.isPresent ? 1 : 0;
      if(delivery_format === EDeliveryOption.SURVEY && lang){
        return await this.createAttempt(uid, options.isTestTaker, test_session_id, lang, testSessionTDSimilarityTags, present_bool_int, 0, delivery_format);
      }
      return await this.createAttempt(uid, options.isTestTaker, test_session_id, null, testSessionTDSimilarityTags, present_bool_int);
    }

    throw new Errors.Forbidden("SESSION_CLOSED");
  }


  public async closeAttempt (test_attempt_id:number, closedByUid:number, forceClose:boolean = false, cache:any = {}, testSessionId?:number, tassConfig?: {id:number, closesTestAttempt: boolean, twtdar_order: number, test_session_id: number, attemptUid: number, ssOrder: number, autoOpenNext: boolean}, forceKeepActiveSubsession:boolean=false, isAutoSubmission:boolean=false ) {

    const is_submitted = forceClose ? undefined : 1;
    // const attempt:ITestAttempt = await this.app.service('db/read/test-attempts').get(test_attempt_id);

    if(tassConfig !== undefined && tassConfig.id !== undefined) {
      const payload:any =  {
        is_submitted,
        last_locked_on: new Date().toISOString(),
        last_locked_by_uid: closedByUid
      }
      if(isAutoSubmission){
        payload["is_auto_submitted"] = 1
        payload["submitted_on"] = dbDateNow(this.app)
      }
      await patchTestAttemptSubSession(this.app, tassConfig.id, payload);
    }

    let patchRecord: any = {
      active_sub_session_id: null,
      last_updated_by_uid: closedByUid
    }

    if(tassConfig === undefined || tassConfig.closesTestAttempt) {
      patchRecord["is_closed"] = 1;
      patchRecord["is_submitted"] = is_submitted;
      patchRecord["submitted_test_session_id"] = testSessionId;
      patchRecord["closed_on"] = dbDateNow(this.app);
      if(isAutoSubmission){
        patchRecord["is_auto_submitted"] = 1;
        patchRecord["auto_submitted_on"] = dbDateNow(this.app)
      }
    }

    if (forceKeepActiveSubsession) {
      delete patchRecord.active_sub_session_id
    }
    try {
      await patchTestAttempt(this.app, test_attempt_id, patchRecord);
    } catch (e) {
      // TODO what if attempt is already closed?
    }

    if(tassConfig?.closesTestAttempt && tassConfig?.autoOpenNext) {
      const nextTwtdarOrder = tassConfig.twtdar_order + 1;
      const nextSsOrder = tassConfig.ssOrder + 1;

      //Attempt to auto-open the next test_attempt if it exists
      const nextTestAttemptRow =  await this.app.service('db/read/test-attempts').db()
        .where('test_session_id', tassConfig.test_session_id)
        .where('twtdar_order', nextTwtdarOrder)
        .where('uid', tassConfig.attemptUid)
        .whereNot('is_invalid', 1)
        .limit(1);

      if(nextTestAttemptRow && nextTestAttemptRow.length > 0) {
        const nextTestAttempt = nextTestAttemptRow[0];

        const nextSubSessionRow = await this.app.service('db/read/test-session-sub-sessions').db()
          .where('test_session_id', tassConfig.test_session_id)
          .where('twtdar_order', nextTwtdarOrder)
          .where('order', nextSsOrder)
          .limit(1)

        if(nextSubSessionRow && nextSubSessionRow.length > 0) {
          const nextSubSession = nextSubSessionRow[0];
          await patchTestAttempt(this.app, nextTestAttempt.id, {
            active_sub_session_id: nextSubSession.id,
          });
        }
      }
    }



    // todo: lively remove
    // this.populateNonResponses({
    //   cache,
    //   resolvedAttempt,
    // });

  }

  // todo: likely remove
  private async populateNonResponses(config:IPopNonResConfig){
    throw new Errors.NotImplemented();
    // const {
    //   cache,
    //   resolvedAttempt,
    // } = config;

    // if (!cache.testSessions) {
    //   cache.testSessions = {};
    // }
    // if (!cache.testSessions[resolvedAttempt.test_session_id]) {
    //   cache.testSessions[resolvedAttempt.test_session_id] = await this.app.service('db/read/test-sessions').get(resolvedAttempt.test_session_id);
    // }
    // const testSession = cache.testSessions[resolvedAttempt.test_session_id];

    // if (!cache.testWindow) {
    //   cache.testWindow = await this.app.service('db/read/test-windows').get(testSession.test_window_id);
    // }
    // const testWindow = cache.testWindow;

    // if (!cache.testDesignFramework) {
    //   cache.testDesignFramework = await this.getTestDesignFramework(testWindow.test_design_id);
    //   if (_.isString(cache.testDesignFramework)) {
    //     cache.testDesignFramework = JSON.parse(cache.testDesignFramework);
    //   }
    //   if (!cache.testDesignFramework.testlets) {
    //     cache.testDesignFramework.testlets = [];
    //   }
    // }
    // const testDesignFramework = cache.testDesignFramework;

    // const testDesign = await this.getTestDesign(''+resolvedAttempt.test_form_id);
    // const itemDataMap = this.getItemDataMap(testDesign, testDesignFramework);
    // const existingResps = <any[]> await this.app.service('db/write/test-attempt-question-responses').find({
    //   query: {
    //     test_attempt_id: resolvedAttempt.id
    //   },
    //   paginate: false
    // });
    // await Bluebird.mapSeries(existingResps, response => {
    //   const testletData = itemDataMap[response.test_question_id];
    //   if (!testletData) {
    //     return;
    //   }
    //   return this.app.service('db/write/test-attempt-question-responses').patch(response.id, testletData)
    // })
    // const skippedQuestionIds = _(testDesign.questionDb)
    //   .keys()
    //   .differenceWith(existingResps, (idFromTestDesign:number, response:{test_question_id:number}) => {
    //     return idFromTestDesign == response.test_question_id
    //   })
    //   .value()
    // await Bluebird.mapSeries(skippedQuestionIds, (qId:number) => {
    //   return this.app.service('db/write/test-attempt-question-responses').create(
    //     _.assign(
    //       {
    //         test_attempt_id: resolvedAttempt.id,
    //         test_question_id: qId,
    //         test_question_version_id: qId,
    //         response_raw: null,
    //         response: null,
    //         updated_on: dbDateNow(this.app),
    //         is_not_seen: true
    //       },
    //       itemDataMap[qId]
    //     )
    //   )
    // });
  }

  // todo: likely remove
  private getItemDataMap (testDesign:any, testDesignFramework:any) {
    throw new Errors.NotImplemented();
    // const testlets = _.filter(testDesignFramework.testlets, (testlet:any) => _.includes(testDesign.testletIds, testlet.id));
    // const itemDataMap:any = {};
    // _.each(testlets, (testlet:any) => {
    //   _.each(testlet.questions, (question:any) => {
    //     const section = _.find(testDesign.sections, (section:any) => _.includes(section.questions, question.id));
    //     itemDataMap[question.label] = {
    //       section_id: testlet.section,
    //       section_form_order: section.questions.indexOf(question.id),
    //       testlet_id: testlet.id,
    //       quadrant_id: testlet.quadrant
    //     }
    //   })
    // });
    // return itemDataMap;
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async patch (id: NullableId, data: {test_attempt_id: number}, params?: Params): Promise<Data> {
    const {schl_class_group_id} = (<any>params).query;
    const {test_attempt_id} = data;


    const heartbeatCtrl = (await getSysConstNumeric(this.app, 'STUDENT_HEARTBEAT_CTRL', true)) as number;
    if (heartbeatCtrl == -1) {
      return {};
    }
    else if (heartbeatCtrl > 0 && heartbeatCtrl < 100) {
      if (Math.random() < heartbeatCtrl/100.0) {
        return {};
      }
    }
    const last_touch_on = new Date().toISOString();
    await patchTestAttempt(this.app, test_attempt_id, {last_touch_on});
    return {};
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  private async getData(props:any[], query:string){
    const db:Knex = this.app.get('knexClientRead');
    const res = await db.raw(query, props);
    return <any[]> res[0];
  }

  async getAssessmentType(uid:any, class_id:any){
    const studentRecord = await dbRawRead(this.app, [uid, class_id], `
      select scts.slug, twtdar.is_questionnaire
        from test_attempts ta
        join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
        join test_sessions ts on ts.id = scts.test_session_id and ts.is_cancelled = 0 and ts.is_closed = 0
        join test_windows tw on tw.id = ts.test_window_id
        join test_window_td_alloc_rules twtdar on twtdar.id = ta.twtdar_id
       where ta.uid = ?
         and ta.is_invalid = 0
         and ta.active_sub_session_id is not null
         and tw.is_active = 1
         and scts.school_class_id = ?
    ;`);
    return studentRecord;
  }
}
