// Initializes the `public/version-manager` service on path `/public/version-manager`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { VersionManager } from './version-manager.class';
import hooks from './version-manager.hooks';

// Add this service to the service type index
declare module '../../../declarations' {
  interface ServiceTypes {
    'public/version-manager': VersionManager & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/version-manager', new VersionManager(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/version-manager');

  service.hooks(hooks);
}
