import { AuthenticationService } from '@feathersjs/authentication';
import { Params } from '@feathersjs/feathers';
import { NotAuthenticated } from '@feathersjs/errors';
import logger from './logger';
const _ = require('lodash');
export interface AuthenticationResult {
    [key: string]: any;
}
export interface AuthenticationRequest {
    strategy?: string;
    [key: string]: any;
}

export class CustomAuthenticationService extends AuthenticationService {

   async create(data:AuthenticationRequest, params:Params) {
    const { entity } = this.configuration;
    const authStrategies = params.authStrategies || this.configuration.authStrategies;

    if (!authStrategies.length) {
      throw new NotAuthenticated('No authentication strategies allowed for creating a JWT (`authStrategies`)');
    }

    let refreshTokenPayload;
    let authResult;

    if (data.action === 'refresh' && !data.refresh_token) {
      throw new NotAuthenticated('No refresh token');
    } 
    else if (data.action === 'refresh') {
      refreshTokenPayload = await this.verifyAccessToken(data.refresh_token);
      authResult = {
        [entity]: refreshTokenPayload[entity],
        authentication: { strategy: data.strategy },
      };
    } 
    else {
      authResult = await this.authenticate(data, params, ...authStrategies);
    }

    if (authResult && authResult.accessToken) {
      return authResult;
    }

    // Check whether MFA is required, local strategy is blocked in this case
    // Allow authentication if MFA is not setup yet
    if(data.strategy == 'local' && data.action != 'refresh') {
      const {userRequiresMFA, isTotpUser, isFirstLoginWithTotp } = await this.app.service('public/auth/register-mfa').find({query: {email: data.email}});
      if (userRequiresMFA && isTotpUser && !isFirstLoginWithTotp) {
        throw new NotAuthenticated('MFA_REQUIRED');
      }
    }

    const [payload, jwtOptions] = await Promise.all([
      this.getPayload(authResult, params),
      this.getTokenOptions(authResult, params)
    ]);
    let tokenPayload;
    if(_.isEmpty(payload)) {
      if (refreshTokenPayload.accountType === 'student' || refreshTokenPayload.account_type === 'student') {
        const {
          email,
          id,
          uid,
          account_type,
          first_name,
          last_name,
          contact_phone,
          contact_email,
          is_claimed,
          timezone,
          alias_uid,
          img_url,
          accountType,
          sch_class_group_id,
          lang,
          sch_class_id,
          sch_class_group_type,
          assistive_tech,
          linear,
          studentOenOrSasn,
          isSasnLogin,
          assessmentType
        } = refreshTokenPayload;
        tokenPayload = {
            email,
            id,
            uid,
            account_type,
            first_name,
            last_name,
            contact_phone,
            contact_email,
            is_claimed,
            timezone,
            alias_uid,
            img_url,
            accountType,
            sch_class_group_id,
            lang,
            sch_class_id,
            sch_class_group_type,
            assistive_tech,
            linear,
            studentOenOrSasn,
            isSasnLogin,
            assessmentType
        }
      } else {
        const {
          email,
          id,
          uid,
          accountType,
          firstName,
          lastName,
          is_totp_user
        } = refreshTokenPayload;
        tokenPayload = {
          email,
          id,
          uid,
          accountType,
          firstName,
          lastName,
          is_totp_user
        }
      }
    }
    else {
      tokenPayload = payload;
    }
    const accessToken = await this.createAccessToken(tokenPayload);

    /**
     * Generate refresh token
     */
    const refreshTokenJwtOptions = {
      ...jwtOptions,
      expiresIn: this.configuration.refreshExpiresIn
    };

    refreshTokenPayload = {
      ...payload,
      // tokenType: 'refresh',
      // [entity]: authResult[entity]
    };


    const refreshToken = await this.createAccessToken(tokenPayload);

    return Object.assign({}, { accessToken, refreshToken:refreshToken }, authResult);
  }


  async getPayload(authResult:any, params:Params) {
    // Call original `getPayload` first
    var payload = await super.getPayload(authResult, params);
    const { user } = authResult;

    if (user) {
      payload = user;
    }

    return payload;
  }

  /**
   * Override remove method to handle logout and token blacklisting
   */
  async remove(id: any, params: Params) {
    // Extract JWT token from Authorization header
    const authHeader = params.headers?.authorization || params.headers?.Authorization;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      const accessToken = authHeader.substring(7);

      this.app.service('auth/jwt-blacklist').create({ token: accessToken }).catch((error: any) => {
        // Don't fail logout if blacklisting fails
        logger.warn('Failed to blacklist token during logout', {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      });
    }

    // Proceed with normal logout
    return super.remove(id, params);
  }
}
