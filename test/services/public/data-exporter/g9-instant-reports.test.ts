import assert from 'assert';
import app from '../../../../src/app';

describe('\'public/data-exporter/g9-instant-reports\' service', () => {
  it('registered the service', () => {
    const service = app.service('public/data-exporter/g9-instant-reports');

    assert.ok(service, 'Registered the service');
  });

  it.skip('can send a confirmation email', () => {
    const service = app.service('public/data-exporter/g9-instant-reports');

    service.sendReportEmail(0, 0, ["<EMAIL>"])
    .then((value) => {
      console.log("G9 report email test", value);
      return assert.ok(true, "Email sent");
    }, (reason) => {
      console.log("G9 report email failed", reason);
      return assert.ok(false, "Some failure to send email")
    });
  });

  it.skip('sends report notification email to default list', () => {
    const service = app.service('public/data-exporter/g9-instant-reports');

    service.sendReportEmail(1, 1)
    .then((value) => {
      console.log("G9 report email test", value);
      return assert.ok(true, "Email sent");
    }, (reason) => {
      console.log("G9 report email failed", reason);
      return assert.ok(false, "Some failure to send email")
    });
  });

  it.skip('renders markdown correctly', () => {
    var marked = require( "marked" );
    const psychRunId = 0;
    const exportId = 0;
    // Determined that the contents cannot be indented at all.
    const emailTemplate = `Hello EQAO team,

The validated output for psych run ${psychRunId} has been completed,
including the psychometric output and ISRs generated.

Please download the reports through [the website](https://eqao.vretta.com/).

For your reference:

- Export ID: ${exportId}
- Psych Run ID: ${psychRunId}

Please let us know if you may have any questions or concerns.`;
    console.log("Email content (raw)", emailTemplate);
    console.log("Email content (rendered)", marked(emailTemplate, { xhtml: true }));
    assert.ok(false, "Force fail for logs")
  });
});

